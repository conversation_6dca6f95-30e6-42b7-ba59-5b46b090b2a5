#!/bin/bash

# Read prompt from stdin
if [ -t 0 ]; then
  echo "Please enter your image prompt:"
  read -r PROMPT
else
  PROMPT=$(cat)
fi

# Check if prompt is empty
if [ -z "$PROMPT" ]; then
  echo "Error: No prompt provided. Please provide a prompt."
  exit 1
fi

# Escape the prompt for JSON
JSONIFIED_PROMPT=$(printf '%s' "$PROMPT" | jq -Rs .)

curl https://api.openai.com/v1/images/generations \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $OPENAI_API_KEY" \
  -d '{
    "model": "dall-e-3",
    "prompt": '"$JSONIFIED_PROMPT"',
    "n": 1,
    "quality": "hd",
    "size": "1792x1024"
  }'

# Output is similar to the following:
# {
#   "created": 1729169037,
#   "data": [
#     {
#       "revised_prompt": "Envision an image that strikes a balance between realism and cinematic flair. In it, a large crocodile emerges mysteriously from the depths of a rain-drenched New York City street, its scales reflecting the ambient light from the city's towering Neo-Victorian structures. The city epitomizes the steampunk aesthetic, replete with buildings featuring steam-powered technology, intricate gadgets, industrial machinery, and the likes. Weathered copper and bronze hues characterize the urban landscape, enhancing the city\u2019s iconic silhouette. An abundance of cogwheels, pipes, and gauge dials permeate the architecture, thus contributing to an overall mystique and otherworldly charm.",
#       "url": "https://oaidalleapiprodscus.blob.core.windows.net/private/org-nxchsPSx9O3ZjluAoc7WoUGO/user-B1I8sd6zLNtdaudpkfIx3WVU/img-RTEpRiX0nPNEzOu8ZSQe6c2E.png?st=2024-10-17T11%3A43%3A57Z&se=2024-10-17T13%3A43%3A57Z&sp=r&sv=2024-08-04&sr=b&rscd=inline&rsct=image/png&skoid=d505667d-d6c1-4a0a-bac7-5c84a87759f8&sktid=a48cca56-e6da-484e-a814-9c849652bcb3&skt=2024-10-16T23%3A24%3A50Z&ske=2024-10-17T23%3A24%3A50Z&sks=b&skv=2024-08-04&sig=U5WvlKDrT85l0W6IaL/3ZcAsvewehR8qqo/qdDRldBE%3D"
#     }
#   ]
# }

