Feature: GitHub Repository Browser
    As a developer
    I want to browse, view, and edit files in a GitHub repository
    So that I can work with repository content efficiently

    Scenario: Display editable parameters
        Given the app is started
        Then editable parameters are shown
        And the "token" parameter for GitHub token is shown
        And the "URL" parameter for repository URL is shown
        And the URL can be a full or partial URL
        And the URL can include path to the repo and subpath to file/folder

    Sc<PERSON>rio: Display reference folder
        Given the app is started
        Then a read-only "refFolder" field is shown

    Scenario: List repository content
        Given editable parameters have been provided
        When the "List" button is pressed or the "refFolder" is changed
        Then the content of the "refFolder" is displayed
        And an item labeled ".." representing the parent folder is shown

    Scenario: Change reference folder
        Given repository content is listed
        When an item representing a folder is double-clicked
        Then the "refFolder" is updated to the selected folder

    Scenario: Open a file
        Given repository content is listed
        When an item representing a file is double-clicked
        Then the content of the file is displayed

    Scenario: Edit file content
        Given a file is open and displayed
        When the "Edit" button is pressed
        Then the file content is shown in an editable text field

    Scenario: Save file content
        Given file content is being edited
        When the "Save" button is pressed
        Then the file content is saved
        And the file content is displayed in a read-only text field

    Scenario: Close file
        Given a file is open and displayed
        When the "Close" button is pressed
        Then the file content display is closed
