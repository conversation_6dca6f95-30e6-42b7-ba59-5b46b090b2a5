import 'package:flutter/material.dart';
import 'services/github/i_github_service.dart';
import 'services/github/github_service.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'GitHub Browser',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const GitHubBrowserPage(),
    );
  }
}

class GitHubBrowserPage extends StatefulWidget {
  const GitHubBrowserPage({super.key});

  @override
  State<GitHubBrowserPage> createState() => _GitHubBrowserPageState();
}

class _GitHubBrowserPageState extends State<GitHubBrowserPage> {
  // Controllers for text fields
  final TextEditingController _tokenController = TextEditingController();
  final TextEditingController _urlController = TextEditingController();
  final TextEditingController _fileContentController = TextEditingController();

  // GitHub service
  GitHubService? _gitHubService;

  // Current folder path (relative to base folder)
  String _currentRelPath = '';

  // List of items in the current folder
  List<GitHubItem> _folderContents = [];

  // Loading state
  bool _isLoading = false;

  // Error message
  String? _errorMessage;

  // Success message
  String? _successMessage;

  // Currently viewed file
  String? _currentFile;

  // Content of the currently viewed file
  String? _fileContent;

  // Edit mode flag
  bool _isEditMode = false;

  // Saving state
  bool _isSaving = false;

  // SHA of the currently viewed file
  String? _currentFileSha;

  @override
  void dispose() {
    _tokenController.dispose();
    _urlController.dispose();
    _fileContentController.dispose();
    super.dispose();
  }

  // Initialize GitHub service with token and URL
  void _initializeService() {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final token = _tokenController.text.trim();
      final url = _urlController.text.trim();

      if (token.isEmpty || url.isEmpty) {
        setState(() {
          _errorMessage = 'Token and URL are required';
          _isLoading = false;
        });
        return;
      }

      _gitHubService = GitHubService(token: token, url: url);
      _currentRelPath = '';
      _loadFolderContents();
    } catch (e) {
      setState(() {
        _errorMessage = 'Error initializing service: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  // Load contents of the current folder
  Future<void> _loadFolderContents() async {
    if (_gitHubService == null) {
      setState(() {
        _errorMessage = 'GitHub service not initialized';
        _isLoading = false;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final contents = await _gitHubService!.listFolder(
        _currentRelPath.isEmpty ? null : _currentRelPath,
      );
      setState(() {
        _folderContents = contents;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading folder contents: ${e.toString()}';
        _isLoading = false;
        _folderContents = [];
      });
    }
  }

  // Navigate to a folder
  void _navigateToFolder(String folderName) {
    if (_currentRelPath.isEmpty) {
      _currentRelPath = folderName;
    } else {
      _currentRelPath = '$_currentRelPath/$folderName';
    }

    _loadFolderContents();
  }

  // Navigate to parent folder
  void _navigateToParentFolder() {
    if (_currentRelPath.isEmpty) {
      return;
    }

    final lastSlashIndex = _currentRelPath.lastIndexOf('/');
    if (lastSlashIndex == -1) {
      _currentRelPath = '';
    } else {
      _currentRelPath = _currentRelPath.substring(0, lastSlashIndex);
    }

    _loadFolderContents();
  }

  // Handle item double tap
  void _handleItemDoubleTap(GitHubItem item) {
    if (item.type == GitHubItemType.folder) {
      _navigateToFolder(item.name);
    } else {
      _loadFileContent(item.name);
    }
  }

  // Load and display file content
  Future<void> _loadFileContent(String fileName) async {
    if (_gitHubService == null) {
      setState(() {
        _errorMessage = 'GitHub service not initialized';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      final filePath = _currentRelPath.isEmpty
          ? fileName
          : '$_currentRelPath/$fileName';

      final result = await _gitHubService!.getFileContent(filePath);
      final content = result['content'] ?? '';
      final sha = result['sha'];

      // Store the SHA for later use when saving
      _currentFileSha = sha;

      // Set the content in both the state variable and the controller
      _fileContentController.text = content;

      setState(() {
        _currentFile = fileName;
        _fileContent = content;
        _isLoading = false;
        _isEditMode = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading file content: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  // Close file view and return to folder view
  void _closeFileView() {
    setState(() {
      _currentFile = null;
      _fileContent = null;
      _isEditMode = false;
      _successMessage = null;
    });
  }

  // Toggle edit mode
  void _toggleEditMode() {
    setState(() {
      _isEditMode = !_isEditMode;
      // If entering edit mode, make sure the controller has the current content
      if (_isEditMode && _fileContent != null) {
        _fileContentController.text = _fileContent!;
      }
    });
  }

  // Save file content
  Future<void> _saveFileContent() async {
    if (_gitHubService == null || _currentFile == null) {
      setState(() {
        _errorMessage = 'GitHub service not initialized or no file selected';
      });
      return;
    }

    final newContent = _fileContentController.text;

    setState(() {
      _isSaving = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      final filePath = _currentRelPath.isEmpty
          ? _currentFile!
          : '$_currentRelPath/$_currentFile';

      await _gitHubService!.updateFileContent(
        filePath,
        newContent,
        sha: _currentFileSha, // Pass the SHA we got when loading the file
        message: 'Update $filePath via GitHub Browser',
      );

      // Refresh the SHA after saving to ensure we have the most current one
      final result = await _gitHubService!.getFileContent(filePath);
      _currentFileSha = result['sha'];

      setState(() {
        _fileContent = newContent;
        _isEditMode = false;
        _isSaving = false;
        _successMessage = 'File saved successfully';
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Error saving file: ${e.toString()}';
        _isSaving = false;
      });
    }
  }

  // Build the folder view widget
  Widget _buildFolderView() {
    return ListView(
      children: [
        // Parent folder navigation
        if (_currentRelPath.isNotEmpty)
          GestureDetector(
            onDoubleTap: _navigateToParentFolder,
            child: ListTile(
              leading: const Icon(Icons.folder),
              title: const Text('..'),
              onTap: _navigateToParentFolder,
            ),
          ),

        // Folder contents
        ..._folderContents.map(
          (item) => GestureDetector(
            onDoubleTap: () => _handleItemDoubleTap(item),
            child: ListTile(
              leading: Icon(
                item.type == GitHubItemType.folder
                    ? Icons.folder
                    : Icons.insert_drive_file,
              ),
              title: Text(item.name),
              onTap: () => _handleItemDoubleTap(item),
            ),
          ),
        ),
      ],
    );
  }

  // Build the file view widget
  Widget _buildFileView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // File header with action buttons
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  _currentFile!,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
              // Edit/Save button
              if (!_isEditMode)
                ElevatedButton.icon(
                  onPressed: _toggleEditMode,
                  icon: const Icon(Icons.edit),
                  label: const Text('Edit'),
                ),
              if (_isEditMode)
                ElevatedButton.icon(
                  onPressed: _isSaving ? null : _saveFileContent,
                  icon: _isSaving
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.save),
                  label: const Text('Save'),
                ),
              const SizedBox(width: 8),
              // Close button
              ElevatedButton.icon(
                onPressed: _closeFileView,
                icon: const Icon(Icons.close),
                label: const Text('Close'),
              ),
            ],
          ),
        ),

        const Divider(),

        // Success message
        if (_successMessage != null)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            child: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green.shade700),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _successMessage!,
                    style: TextStyle(color: Colors.green.shade900),
                  ),
                ),
              ],
            ),
          ),

        // File content
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: _isEditMode
                ? TextField(
                    controller: _fileContentController,
                    maxLines: null,
                    expands: true,
                    style: const TextStyle(fontFamily: 'monospace'),
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.all(16.0),
                    ),
                  )
                : SingleChildScrollView(
                    child: SelectableText(
                      _fileContent!,
                      style: const TextStyle(fontFamily: 'monospace'),
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: const Text('GitHub Browser'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Parameters section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Parameters',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _tokenController,
                      decoration: const InputDecoration(
                        labelText: 'GitHub Token',
                        border: OutlineInputBorder(),
                        hintText: 'Enter your GitHub token',
                      ),
                      obscureText: true,
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _urlController,
                      decoration: const InputDecoration(
                        labelText: 'Repository URL',
                        border: OutlineInputBorder(),
                        hintText: 'https://github.com/owner/repo',
                      ),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _isLoading ? null : _initializeService,
                      child: const Text('List Contents'),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Current folder section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Current Folder',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _gitHubService == null
                          ? 'Not initialized'
                          : _gitHubService!.basePath +
                                (_currentRelPath.isEmpty
                                    ? ''
                                    : '/$_currentRelPath'),
                      style: const TextStyle(fontFamily: 'monospace'),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Error message
            if (_errorMessage != null)
              Card(
                color: Colors.red.shade100,
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    _errorMessage!,
                    style: TextStyle(color: Colors.red.shade900),
                  ),
                ),
              ),

            if (_errorMessage != null) const SizedBox(height: 16),

            // Success message (only shown in folder view, file view has its own)
            if (_successMessage != null && _currentFile == null)
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 8.0,
                  vertical: 4.0,
                ),
                child: Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.green.shade700),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _successMessage!,
                        style: TextStyle(color: Colors.green.shade900),
                      ),
                    ),
                  ],
                ),
              ),

            if (_successMessage != null && _currentFile == null)
              const SizedBox(height: 8),

            // Folder contents or file content
            Expanded(
              child: Card(
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _gitHubService == null
                    ? const Center(
                        child: Text('Enter token and URL to browse repository'),
                      )
                    : _currentFile != null && _fileContent != null
                    ? _buildFileView()
                    : _buildFolderView(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
