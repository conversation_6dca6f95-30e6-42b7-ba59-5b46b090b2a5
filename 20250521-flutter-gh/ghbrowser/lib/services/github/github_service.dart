/*
 * Copyright (c) 2025-present unTill Software Development Group B. V.
 * <AUTHOR>
 */

import 'i_github_service.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;

/// Implementation of [IGitHubService] that interacts with the GitHub API.
///
/// This class uses direct HTTP requests to interact with the GitHub API
/// without relying on any external libraries.
class GitHubService implements IGitHubService {
  /// The GitHub API token used for authentication.
  final String token;

  /// The base URL of the repository.
  final String url;

  /// The owner of the repository.
  final String owner;

  /// The name of the repository.
  final String repo;

  /// The base path within the repository.
  final String basePath;

  /// Constructor for [GitHubService].
  ///
  /// This constructor parses the URL, validates the token, and initializes the service.
  /// [token] is the GitHub API token used for authentication.
  /// [url] is the URL of the repository, which can include path to the repo
  /// and subpath to a specific file or folder.
  GitHubService({required this.token, required this.url})
    : owner = _parseUrl(url)['owner']!,
      repo = _parseUrl(url)['repo']!,
      basePath = _parseUrl(url)['path'] ?? '';

  /// Parses a GitHub URL to extract owner, repo, and path.
  ///
  /// [url] The GitHub URL to parse.
  /// Returns a map containing 'owner', 'repo', and 'path' keys.
  static Map<String, String> _parseUrl(String url) {
    // Example URLs:
    // - https://github.com/owner/repo/blob/main/path/to/file.txt (file)
    // - https://github.com/owner/repo/tree/main/path/to/folder (folder)
    // - https://github.com/owner/repo (repository root)

    // Remove protocol and domain
    final urlWithoutProtocol = url.replaceFirst(
      RegExp(r'^https?://github\.com/'),
      '',
    );

    // Split by '/'
    final parts = urlWithoutProtocol.split('/');

    if (parts.length < 2) {
      throw FormatException('Invalid GitHub URL: $url');
    }

    final owner = parts[0];
    final repo = parts[1];

    // Extract path if it exists
    String? path;
    if (parts.length > 4) {
      if (parts[2] == 'blob' || parts[2] == 'tree') {
        // Skip 'blob'/'tree' and branch name (parts[2] and parts[3])
        path = parts.sublist(4).join('/');
      }
    }

    return {'owner': owner, 'repo': repo, if (path != null) 'path': path};
  }

  /// Normalizes a path by combining base path and relative path,
  /// ensuring no double slashes and no leading slash in the final path.
  ///
  /// [relPath] The relative path to combine with the base path.
  /// Returns a normalized path string.
  String _normalizePath(String? relPath) {
    String path;
    if (relPath == null || relPath.isEmpty) {
      path = basePath;
    } else if (basePath.isEmpty) {
      path = relPath;
    } else {
      path = '$basePath/$relPath';
    }

    // Clean up the path by removing any double slashes
    path = path.replaceAll(RegExp(r'//+'), '/');

    // Remove leading slash if present
    if (path.startsWith('/')) {
      path = path.substring(1);
    }

    return path;
  }

  @override
  Future<List<GitHubItem>> listFolder([String? relPath]) async {
    // Get normalized path
    final path = _normalizePath(relPath);

    final uri = Uri.parse(
      'https://api.github.com/repos/$owner/$repo/contents/$path',
    );

    final response = await http.get(
      uri,
      headers: {
        'Accept': 'application/vnd.github+json',
        'Authorization': 'Bearer $token',
        'X-GitHub-Api-Version': '2022-11-28',
      },
    );

    if (response.statusCode != 200) {
      throw Exception(
        'Failed to list folder: ${response.statusCode} ${response.body}',
      );
    }

    final List<dynamic> data = jsonDecode(response.body);
    return data.map((item) {
      final name = item['name'] as String;
      final type = item['type'] == 'dir'
          ? GitHubItemType.folder
          : GitHubItemType.file;

      return GitHubItem(name: name, type: type);
    }).toList();
  }

  @override
  Future<Map<String, String>> getFileContent(String relPath) async {
    // Get normalized path
    final path = _normalizePath(relPath);

    final uri = Uri.parse(
      'https://api.github.com/repos/$owner/$repo/contents/$path',
    );

    String content;
    String? sha;

    try {
      // First get metadata to retrieve the SHA
      final metadataResponse = await http.get(
        uri,
        headers: {
          'Accept': 'application/vnd.github+json',
          'Authorization': 'Bearer $token',
          'X-GitHub-Api-Version': '2022-11-28',
        },
      );

      if (metadataResponse.statusCode == 200) {
        final fileData = jsonDecode(metadataResponse.body);
        if (fileData is Map && fileData.containsKey('sha')) {
          sha = fileData['sha'] as String;

          // Now get the raw content
          if (fileData.containsKey('content')) {
            // Content is base64 encoded
            final base64Content = fileData['content'] as String;
            final cleanBase64 = base64Content.replaceAll(RegExp(r'\s+'), '');
            content = utf8.decode(base64.decode(cleanBase64));
          } else {
            // Try raw request as fallback
            final rawResponse = await http.get(
              uri,
              headers: {
                'Accept': 'application/vnd.github.raw+json',
                'Authorization': 'Bearer $token',
                'X-GitHub-Api-Version': '2022-11-28',
              },
            );

            if (rawResponse.statusCode == 200) {
              content = utf8.decode(rawResponse.bodyBytes);
            } else {
              throw Exception('File content not found in response');
            }
          }
        } else {
          throw Exception('SHA not found in file metadata');
        }
      } else {
        throw Exception(
          'Failed to get file metadata: ${metadataResponse.statusCode} ${metadataResponse.body}',
        );
      }

      return {'content': content, 'sha': sha};
    } catch (e) {
      throw Exception('Failed to get file content: $e');
    }
  }

  @override
  Future<void> updateFileContent(
    String relPath,
    String content, {
    String? sha,
    String? message,
  }) async {
    // Get normalized path
    final path = _normalizePath(relPath);

    final uri = Uri.parse(
      'https://api.github.com/repos/$owner/$repo/contents/$path',
    );

    // Prepare the request body
    final Map<String, dynamic> requestBody = {
      'message': message ?? 'Update $relPath',
      'content': base64.encode(utf8.encode(content)),
    };

    // Include SHA if provided (required for updates)
    if (sha != null) {
      requestBody['sha'] = sha;
    }

    try {
      // Send the PUT request to create or update the file
      final response = await http.put(
        uri,
        headers: {
          'Accept': 'application/vnd.github+json',
          'Authorization': 'Bearer $token',
          'X-GitHub-Api-Version': '2022-11-28',
          'Content-Type': 'application/json',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode != 200 && response.statusCode != 201) {
        throw Exception(
          'Failed to update file content: ${response.statusCode} ${response.body}',
        );
      }
    } catch (e) {
      if (e is Exception) {
        rethrow;
      } else {
        throw Exception('Error during file update: $e');
      }
    }
  }
}
