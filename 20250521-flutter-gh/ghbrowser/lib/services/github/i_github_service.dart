/*
 * Copyright (c) 2025-present unTill Software Development Group B. V. 
 * <AUTHOR>
 */

/// Interface for interacting with GitHub repositories.
///
/// This interface defines methods for browsing and editing GitHub repositories.
/// It allows for listing folder contents, navigating between folders, and
/// viewing and editing file content. Implementations of this interface handle
/// authentication and API communication with GitHub.
///
/// All paths are relative to the BaseFolder, defined by the parameters of the implementation factory.
/// This approach allows for operations to be restricted to a specific directory within a repository.
///
/// `relPath` parameters used throughout this interface denote paths relative to the BaseFolder.
abstract class IGitHubService {
  /// Returns a list of items (files and folders) in the specified path.
  ///
  /// [relPath] The relative path to list. If not provided or null, lists the contents of BaseFolder.
  /// Returns a Future that completes with a list of GitHubItem objects representing the contents.
  Future<List<GitHubItem>> listFolder([String? relPath]);

  /// Gets the content of a file.
  ///
  /// [relPath] The relative path to the file whose content should be retrieved.
  /// Returns a Future that completes with a map containing the file content as a String
  /// and the SHA needed for updates.
  Future<Map<String, String>> getFileContent(String relPath);

  /// Updates the content of a file.
  ///
  /// Creates the file if it doesn't exist, otherwise overwrites the existing content.
  ///
  /// [relPath] The relative path to the file to update.
  /// [content] The new content to write to the file.
  /// [sha] The SHA of the file being updated. Required for existing files, null for new files.
  /// [message] Optional commit message. If not provided, a default message will be used.
  Future<void> updateFileContent(
    String relPath,
    String content, {
    String? sha,
    String? message,
  });
}

/// Represents an item (file or folder) in a GitHub repository.
///
/// This class provides a uniform way to handle both files and folders
/// when browsing repository contents.
class GitHubItem {
  /// The name of the item (filename or folder name).
  final String name;

  /// The type of the item (file or folder).
  /// Determines how the item should be handled in the UI and operations.
  final GitHubItemType type;

  /// Creates a new [GitHubItem].
  ///
  /// [name] The name of the file or folder.
  /// [type] The type (file or folder) of this item.
  GitHubItem({required this.name, required this.type});
}

/// Enum representing the type of a GitHub item.
enum GitHubItemType {
  /// A file.
  file,

  /// A folder.
  folder,
}
