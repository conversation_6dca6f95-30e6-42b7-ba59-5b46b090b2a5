# Requirements

## Features

- [GitHub Repository Browser](browse.feature)
  - [Description in markdown format](browse.md)

## Implementation

- Interface `IGitHubService` to work with github
- Implementation of `IGitHubService`
  - `GitHubService`
  - Factory parameters
    - Token
    - URL: can include path to the repo and subpath to the file/folder
  - Use POST/GET requests to interact with the GitHub API, do not use any library
