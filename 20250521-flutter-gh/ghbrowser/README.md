# GitHub Browser

A Flutter web application for browsing GitHub repositories.

## Features

- Browse GitHub repositories and their contents
- Navigate through folders and files
- View file contents
- Authentication with GitHub token

## Getting Started

### Prerequisites

- Flutter SDK (latest stable version)
- A GitHub account and personal access token

### Installation

1. Clone this repository
2. Run `flutter pub get` to install dependencies
3. Run the app with `flutter run -d chrome`

## Usage

1. Enter your GitHub personal access token
2. Enter the URL of the repository you want to browse
3. Navigate through folders by double-tapping on them
4. View file contents by selecting files

## Building for Web

```text
flutter build web
```

The built web app will be available in the `build/web` directory.

## Addressed Issues

- [Automated Testing: Implement IGitHubService](https://untill.atlassian.net/issues/AIR-854?filter=10057)