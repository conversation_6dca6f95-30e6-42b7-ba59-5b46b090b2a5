# Browse repo feature

- Browse repo
- `Params`:
  - Given app started
  - Then editable params are shown
    - `token`: GitHub token
    - `URL`: URL of the repo
      - URL can be a full URL or a partial URL
      - URL can include path to the repo and subpath to the file/folder
- `refFolder`:
  - Given app started
  - Then readonly `refFolder` is shown
- `List`:
  - Given completed: `params`
  - And the "List" button is pressed or the `refFolder` is changed
  - Then show the content of the `refFolder`
  - And show the ".." item that represents the parent folder
- `Change refFolder`:
  - Given `List` is completed
  - And the item that represents the folder is double clicked
  - Then update the `refFolder` to the selected folder
- `Open file`:  
  - Given `List` is completed
  - And the item that represents the file is double clicked
  - Then show the file content
- `Edit file content`
  - Given `Open file` is active
  - And the "Edit" button is pressed
  - Then show the file content in an editable text field
- `Save file content`  
  - Given `Edit file content` is active
  - And the "Save" button is pressed
  - Then save the file content
  - And show the file content in a read-only text field
- `Close file`  
  - Given `Open file` is active
  - And the "Close" button is pressed
  - Then close the file content