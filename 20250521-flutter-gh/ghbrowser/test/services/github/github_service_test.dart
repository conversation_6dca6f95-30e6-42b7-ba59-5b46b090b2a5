import 'package:flutter_test/flutter_test.dart';
import 'package:ghbrowser/services/github/github_service.dart';

void main() {
  group('GitHubService', () {
    test(
      'constructor - should parse URL with blob path and initialize service',
      () {
        // Arrange
        const token = 'test_token';
        const url = 'https://github.com/owner/repo/blob/main/path/to/file.txt';

        // Act
        final service = GitHubService(token: token, url: url);

        // Assert
        expect(service, isA<GitHubService>());
        expect(service.token, equals(token));
        expect(service.url, equals(url));
        expect(service.owner, equals('owner'));
        expect(service.repo, equals('repo'));
        expect(service.basePath, equals('path/to/file.txt'));
      },
    );

    test(
      'constructor - should parse URL with tree path and initialize service',
      () {
        // Arrange
        const token = 'test_token';
        const url = 'https://github.com/owner/repo/tree/main/path/to/folder';

        // Act
        final service = GitHubService(token: token, url: url);

        // Assert
        expect(service, isA<GitHubService>());
        expect(service.token, equals(token));
        expect(service.url, equals(url));
        expect(service.owner, equals('owner'));
        expect(service.repo, equals('repo'));
        expect(service.basePath, equals('path/to/folder'));
      },
    );

    test('constructor - should handle URL without path', () {
      // Arrange
      const token = 'test_token';
      const url = 'https://github.com/owner/repo';

      // Act
      final service = GitHubService(token: token, url: url);

      // Assert
      expect(service, isA<GitHubService>());
      expect(service.token, equals(token));
      expect(service.url, equals(url));
      expect(service.owner, equals('owner'));
      expect(service.repo, equals('repo'));
      expect(service.basePath, equals(''));
    });

    test('constructor - should handle URL with nested subfolder path', () {
      // Arrange
      const token = 'test_token';
      const url = 'https://github.com/maxim-ge/gmp-private/tree/main/airsbp3';

      // Act
      final service = GitHubService(token: token, url: url);

      // Assert
      expect(service, isA<GitHubService>());
      expect(service.token, equals(token));
      expect(service.url, equals(url));
      expect(service.owner, equals('maxim-ge'));
      expect(service.repo, equals('gmp-private'));
      expect(service.basePath, equals('airsbp3'));
    });

    test('constructor - should throw FormatException for invalid URL', () {
      // Arrange
      const token = 'test_token';
      const url = 'https://github.com/invalid';

      // Act & Assert
      expect(
        () => GitHubService(token: token, url: url),
        throwsA(isA<FormatException>()),
      );
    });
  });

  group('GitHubService path handling', () {
    test('should correctly handle paths with tree URLs', () {
      // Arrange
      const token = 'test_token';
      const url = 'https://github.com/owner/repo/tree/main/base/path';
      final service = GitHubService(token: token, url: url);

      // Act & Assert - Test with various relative paths
      expect(service.basePath, equals('base/path'));

      // These tests access a private method, so we can't directly test _normalizePath
      // Instead, we'll test the behavior through the public API in integration tests
    });

    test('should correctly handle paths with blob URLs', () {
      // Arrange
      const token = 'test_token';
      const url = 'https://github.com/owner/repo/blob/main/base/path/file.txt';
      final service = GitHubService(token: token, url: url);

      // Act & Assert
      expect(service.basePath, equals('base/path/file.txt'));
    });

    test('should correctly handle deep nested paths', () {
      // Arrange
      const token = 'test_token';
      const url =
          'https://github.com/owner/repo/tree/main/very/deep/nested/path/structure';
      final service = GitHubService(token: token, url: url);

      // Act & Assert
      expect(service.basePath, equals('very/deep/nested/path/structure'));
    });
  });
}
