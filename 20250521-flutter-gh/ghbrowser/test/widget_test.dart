// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:ghbrowser/main.dart';

void main() {
  testWidgets('GitHub Browser UI test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MyApp());

    // Verify that the initial UI elements are present
    expect(find.text('GitHub Browser'), findsOneWidget);
    expect(find.text('Parameters'), findsOneWidget);
    expect(find.text('GitHub Token'), findsOneWidget);
    expect(find.text('Repository URL'), findsOneWidget);
    expect(find.text('List Contents'), findsOneWidget);
    expect(find.text('Current Folder'), findsOneWidget);
    expect(find.text('Not initialized'), findsOneWidget);
    expect(
      find.text('Enter token and URL to browse repository'),
      findsOneWidget,
    );
  });
}
