# AI-Powered Menu Extraction from Images and Files

- **Motivation**: [AIR-1348](AIR-1348.md)
- **Description**: This research evaluates AI-powered extraction of menu information from restaurant images, focusing on Dutch VAT compliance requirements.
- **Author**: <PERSON>skin
- **Date**: 2023-08-14

## Abstract

This research evaluates AI-powered extraction of menu information from restaurant images, focusing on Dutch VAT compliance requirements. The study develops a comprehensive prompt engineering approach to extract hierarchical menu structures (categories, groups, departments, articles) along with pricing and appropriate VAT classifications (9% reduced rate for food/non-alcoholic beverages, 21% standard rate for alcohol/luxury items).

The methodology employs OpenAI's GPT Vision model to process menu images and return structured JSON output containing restaurant information, complete menu hierarchy, pricing data, and confidence scores for each extraction category. The approach addresses common OCR challenges including blurred text, handwritten menus, and poor lighting conditions by incorporating confidence assessment and problem identification.

Key contributions include a standardized menu hierarchy model aligned with Dutch restaurant operations, automated VAT rate classification based on item categories, and a robust confidence scoring system that identifies extraction quality issues. The research provides a foundation for automated menu digitization systems that can support POS integration and regulatory compliance in the Dutch hospitality sector.

---

## Background

### Menu Hierarchy Example

https://help.air.untill.com/features/products

- Starters
  - Cold Starters
    - Gaspacho
    - Cold Salmon
- Main Dishes
  - Burgers
  - Pasta
- Alcohol
  - Long Drinks
  - Beers
  - Vines
- Non Alcohol
  - Coctails
  - Hot Drinks
    - Tea
    - Coffee
  - Cold Drinks
    - Orange Juice
    - Sparkling Water

### Dutch VAT

- [ChatGPT: Dutch VAT rates overview](https://chatgpt.com/c/689de065-607c-8327-ad8f-b47d1b9ed4b1)

Summary Table:

| Sector                            | 9% Reduced VAT Applies To                 | 21% Standard VAT Applies To               |
|-----------------------------------|-------------------------------------------|-------------------------------------------|
| **Restaurants / Café (in-house)** | Meals, snacks, non-alcoholic beverages    | Alcoholic beverages, luxury services      |
| **Takeaway Food**                 | All food, snacks, non-alcoholic drinks    | –                                         |
| **Catering / Special services**   | If deemed essential—rare; case-specific   | Typically, luxury or specialized catering |
| **Retail Essentials**             | Food items, basic goods (groceries, etc.) | Non-essential or luxury retail items      |

---

## Method

### Create a prompt

Prompt to detect this info:

- Articles (names and prices)
- Departments
- Groups
- VAT rates: Reduced/Standard
- Detection confidence (e.g., recognized correctly: 98% of articles, 95% of departments, 96% of groups, 91% of VAT rates)
- Restaurant name

---

## Results

- [prompt.md](images/prompt.md)
