import sys
import os
import json
import base64
import requests
import time
import argparse
from PIL import Image

# OpenAI API Key
api_key = os.getenv("OPENAI_API_KEY")


# Function to resize image if needed
def resize_image(image_path, max_size):
    """Resize image so the longest side is max_size pixels"""
    with Image.open(image_path) as img:
        # Get current dimensions
        width, height = img.size

        # If image is already smaller than max_size, return original
        if max(width, height) <= max_size:
            return image_path

        # Calculate new dimensions
        if width > height:
            new_width = max_size
            new_height = int(height * max_size / width)
        else:
            new_height = max_size
            new_width = int(width * max_size / height)

        # Resize image
        resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # Save to temporary file
        temp_path = f"temp_resized_{os.path.basename(image_path)}"
        resized_img.save(temp_path, quality=95)

        return temp_path


# Function to encode the image
def encode_image(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode("utf-8")


# Parse command line arguments
parser = argparse.ArgumentParser(
    description="Process menu image with OpenAI Vision API"
)
parser.add_argument("image_path", help="Path to the image file")
parser.add_argument("prompt_path", help="Path to the prompt file")
parser.add_argument(
    "--model", default="gpt-4o", help="OpenAI model to use (default: gpt-4o)"
)
parser.add_argument(
    "--detail",
    default="low",
    choices=["low", "high"],
    help="Image detail level (default: low)",
)
parser.add_argument(
    "--crop",
    type=int,
    default=1024,
    help="Resize longest side to this many pixels (default: 1024)",
)

args = parser.parse_args()

image_path = args.image_path
prompt_path = args.prompt_path
model = args.model
detail = args.detail
crop_size = args.crop

# Check if the image file exists
if not os.path.exists(image_path):
    print(f"Error: Image file '{image_path}' not found.")
    sys.exit(1)

# Check if the prompt file exists
if not os.path.exists(prompt_path):
    print(f"Error: Prompt file '{prompt_path}' not found.")
    sys.exit(1)

print(f"Analyzing image {image_path}...", file=sys.stderr)
print(f"Model: {model}, Detail: {detail}, Crop: {crop_size}", file=sys.stderr)

# Resize image if needed
if crop_size > 0:
    print(f"Resizing image to max {crop_size}px...", file=sys.stderr)
    processed_image_path = resize_image(image_path, crop_size)
else:
    processed_image_path = image_path

print("Encoding image...", file=sys.stderr)
# Getting the base64 string
base64_image = encode_image(processed_image_path)

# Clean up temporary file if created
if processed_image_path != image_path and os.path.exists(processed_image_path):
    os.remove(processed_image_path)

headers = {"Content-Type": "application/json", "Authorization": f"Bearer {api_key}"}

print("Generating payload...", file=sys.stderr)
# Open the prompt file and read its contents
with open(prompt_path, "r") as file:
    prompt = file.read()

payload = {
    "model": model,
    "messages": [
        {
            "role": "user",
            "content": [
                {"type": "text", "text": f"{prompt}"},
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{base64_image}",
                        "detail": detail,
                    },
                },
            ],
        }
    ],
    "max_tokens": 2000,
}

# Record the start time
response_start_time = time.time()

print("Sending request to api.openai.com...", file=sys.stderr)
response = requests.post(
    "https://api.openai.com/v1/chat/completions", headers=headers, json=payload
)

# Calculate and print the time spent
time_spent = time.time() - response_start_time

print(f"Time spent: {time_spent:.2f} seconds", file=sys.stderr)

response_json = response.json()

# Check if the response contains an error
if "error" in response_json:
    error_info = response_json["error"]
    print(f"API Error: {error_info.get('message', 'Unknown error')}", file=sys.stderr)
    print(f"Error type: {error_info.get('type', 'Unknown')}", file=sys.stderr)
    print(f"Error code: {error_info.get('code', 'Unknown')}", file=sys.stderr)
    if "param" in error_info:
        print(f"Parameter: {error_info['param']}", file=sys.stderr)
    sys.exit(1)

# Debug: Print the response status and content if there's an error
if response.status_code != 200:
    print(f"API Error - Status Code: {response.status_code}", file=sys.stderr)
    print(f"Response: {response_json}", file=sys.stderr)
    sys.exit(1)

# Check if the response has the expected structure
if "choices" not in response_json:
    print(f"Unexpected API response structure:", file=sys.stderr)
    print(f"Response: {response_json}", file=sys.stderr)
    sys.exit(1)

if not response_json["choices"]:
    print("No choices in API response", file=sys.stderr)
    print(f"Response: {response_json}", file=sys.stderr)
    sys.exit(1)

content = response_json["choices"][0]["message"]["content"]

print(content)
