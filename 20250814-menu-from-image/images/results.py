"""

Script processes results from the given folder and prepare there a reports.md file

Parameters:
- first: path to the folder with the results from the process.py

Behavior:

- For each resFile in {image_basename}.json
  - Find pair {image_basename}_stdouterr.txt and extract
    - timeSpent
      - From the line like: Time spent: 42.70 seconds
    - imageName
      - from the line like: Analyzing image 01.png...
- Using these results create a results.md in the given folder
- results.md contains two parts, Images and Summary

```markdown

## Images

### {imageName}

<details>
<summary>Image:</summary>

<kbd>![{imageName}](../{imageName})</kbd>

</details>

- Output: [{image_basename}.json]({image_basename}.json)
- Time spent: {timeSpent}
```


Summary part contains a table with two columns sorted by timeSpent in descending order
- {imageName}
- {timeSpent}
"""

import sys
import os
import glob
import re
from pathlib import Path


def extract_info_from_stdouterr(stdouterr_path):
    """Extract time spent, image name, and processing parameters from stdouterr file"""
    time_spent = None
    image_name = None
    model = None
    detail = None
    crop = None

    try:
        with open(stdouterr_path, "r", encoding="utf-8") as f:
            content = f.read()

            # Extract time spent from line like: "Time spent: 42.70 seconds"
            time_match = re.search(r"Time spent: ([\d.]+) seconds", content)
            if time_match:
                time_spent = float(time_match.group(1))

            # Extract image name from line like: "Analyzing image 01.png..."
            image_match = re.search(r"Analyzing image (.+?)\.\.\.", content)
            if image_match:
                image_name = image_match.group(1)

            # Extract processing parameters from line like: "Model: gpt-4o, Detail: low, Crop: 1024"
            params_match = re.search(
                r"Model: ([^,]+), Detail: ([^,]+), Crop: (\d+)", content
            )
            if params_match:
                model = params_match.group(1)
                detail = params_match.group(2)
                crop = int(params_match.group(3))

    except Exception as e:
        print(f"Error reading {stdouterr_path}: {e}")

    return time_spent, image_name, model, detail, crop


def process_results_folder(results_folder):
    """Process all JSON files in the results folder and extract information"""
    results = []

    # Find all JSON files
    json_files = glob.glob(os.path.join(results_folder, "*.json"))

    for json_file in json_files:
        json_path = Path(json_file)
        image_basename = json_path.stem

        # Find corresponding stdouterr file
        stdouterr_file = os.path.join(results_folder, f"{image_basename}_stdouterr.txt")

        if os.path.exists(stdouterr_file):
            time_spent, image_name, model, detail, crop = extract_info_from_stdouterr(
                stdouterr_file
            )

            if time_spent is not None and image_name is not None:
                results.append(
                    {
                        "image_basename": image_basename,
                        "image_name": image_name,
                        "time_spent": time_spent,
                        "model": model,
                        "detail": detail,
                        "crop": crop,
                        "json_file": json_file,
                        "stdouterr_file": stdouterr_file,
                    }
                )
            else:
                print(f"Warning: Could not extract info from {stdouterr_file}")
        else:
            print(f"Warning: No stdouterr file found for {json_file}")

    return results


def generate_markdown_report(results):
    """Generate the markdown report"""
    report_lines = []

    # Header
    report_lines.append("# Menu Processing Results")
    report_lines.append("")

    # Processing Parameters section
    report_lines.append("## Processing Parameters")
    report_lines.append("")

    # Extract unique parameters from results
    models = set()
    details = set()
    crops = set()

    for result in results:
        if result.get("model"):
            models.add(result["model"])
        if result.get("detail"):
            details.add(result["detail"])
        if result.get("crop"):
            crops.add(result["crop"])

    if models or details or crops:
        if models:
            report_lines.append(f"- **Model(s):** {', '.join(sorted(models))}")
        else:
            report_lines.append("- **Model(s):** gpt-4o")
        if details:
            report_lines.append(f"- **Detail level(s):** {', '.join(sorted(details))}")
        else:
            report_lines.append("- **Detail level(s):** low")
        if crops:
            crop_list = ", ".join(str(c) for c in sorted(crops))
            report_lines.append(f"- **Crop size(s):** {crop_list}px")
        else:
            report_lines.append("- **Crop size(s):** 1024px")
    else:
        # Use default parameters when none can be identified
        report_lines.append("- **Model(s):** gpt-4o")
        report_lines.append("- **Detail level(s):** low")
        report_lines.append("- **Crop size(s):** 1024px")

    report_lines.append("")

    # Images section
    report_lines.append("## Images")
    report_lines.append("")

    # Helper function to strip prefix from image name
    def strip_prefix(name):
        if name.startswith(".\\"):
            return name[2:]
        elif name.startswith("./"):
            return name[2:]
        return name

    # Sort by normalized image name for the Images section
    sorted_by_name = sorted(results, key=lambda x: strip_prefix(x["image_name"]))

    for result in sorted_by_name:
        image_name = result["image_name"]
        image_basename = result["image_basename"]
        time_spent = result["time_spent"]

        # Strip optional prefix like ".\" from image name for display
        display_name = strip_prefix(image_name)

        report_lines.append(f"### {display_name}")
        report_lines.append("")
        report_lines.append("<details>")
        report_lines.append("<summary>Image:</summary>")
        report_lines.append("")
        report_lines.append(f"<kbd>![{display_name}](../{display_name})</kbd>")
        report_lines.append("")
        report_lines.append("</details>")
        report_lines.append("")
        report_lines.append(f"- Output: [{image_basename}.json]({image_basename}.json)")
        report_lines.append(f"- Time spent: {time_spent} seconds")
        report_lines.append("")

    # Summary section
    report_lines.append("## Time Spent")
    report_lines.append("")
    report_lines.append("| Image | Time Spent (seconds) |")
    report_lines.append("|-------|---------------------|")

    # Sort by time spent in descending order for summary
    sorted_by_time = sorted(results, key=lambda x: x["time_spent"], reverse=True)

    for result in sorted_by_time:
        image_name = result["image_name"]
        time_spent = result["time_spent"]

        # Strip optional prefix like ".\" from image name for table
        display_name = strip_prefix(image_name)

        report_lines.append(f"| {display_name} | {time_spent} |")

    report_lines.append("")

    # Statistics
    if results:
        total_time = sum(r["time_spent"] for r in results)
        avg_time = total_time / len(results)
        min_time = min(r["time_spent"] for r in results)
        max_time = max(r["time_spent"] for r in results)

        report_lines.append("### Statistics")
        report_lines.append("")
        report_lines.append(f"- **Total images processed:** {len(results)}")
        report_lines.append(f"- **Total time spent:** {total_time:.2f} seconds")
        report_lines.append(f"- **Average time per image:** {avg_time:.2f} seconds")
        report_lines.append(f"- **Fastest processing:** {min_time:.2f} seconds")
        report_lines.append(f"- **Slowest processing:** {max_time:.2f} seconds")

    return "\n".join(report_lines)


def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("Usage: python results.py <results_folder>")
        print("Example: python results.py results-01")
        sys.exit(1)

    results_folder = sys.argv[1]

    if not os.path.exists(results_folder):
        print(f"Error: Results folder '{results_folder}' not found.")
        sys.exit(1)

    if not os.path.isdir(results_folder):
        print(f"Error: '{results_folder}' is not a directory.")
        sys.exit(1)

    print(f"Processing results from folder: {results_folder}")

    # Process the results folder
    results = process_results_folder(results_folder)

    if not results:
        print("No valid results found in the folder.")
        sys.exit(1)

    print(f"Found {len(results)} processed images")

    # Generate the markdown report
    markdown_content = generate_markdown_report(results)

    # Write the report to results.md in the results folder
    report_path = os.path.join(results_folder, "results.md")

    try:
        with open(report_path, "w", encoding="utf-8") as f:
            f.write(markdown_content)

        print(f"✓ Report generated successfully: {report_path}")

    except Exception as e:
        print(f"Error writing report: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
