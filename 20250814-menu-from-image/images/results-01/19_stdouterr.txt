=== STDOUT ===
```json
{
  "restaurant": {
    "name": "Not visible",
    "phone": "Not visible",
    "address": "Not visible"
  },
  "menu": {
    "groups": [
      {
        "name": "Starters",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Cold Starters",
            "articles": [
              {
                "name": "Rosbief with French mayo & soy dressing",
                "price": 10.50
              },
              {
                "name": "Hummus with avocado & grilled vegetables",
                "price": 9.50
              },
              {
                "name": "Caprese with mozzarella, tomato, pesto & balsamic",
                "price": 9.50
              },
              {
                "name": "<PERSON><PERSON>i with mustard & cheddar",
                "price": 10.50
              },
              {
                "name": "Kalkoen with truffle mayo & pancetta",
                "price": 9.95
              },
              {
                "name": "Gerookte zalm with cream cheese & capers",
                "price": 10.75
              }
            ]
          }
        ]
      }
    ]
  },
  "confidence": {
    "articles": 95,
    "articles_problem": "",
    "departments": 95,
    "departments_problem": "",
    "groups": 95,
    "groups_problem": "",
    "vat_rates": 98,
    "vat_rates_problem": "",
    "restaurant_name": 50,
    "restaurant_name_problem": "not visible",
    "restaurant_phone": 50,
    "restaurant_phone_problem": "not visible",
    "restaurant_address": 50,
    "restaurant_address_problem": "not visible"
  }
}
```

=== STDERR ===
Analyzing image .\19.jpeg...
Model: gpt-4o, Detail: low, Crop: 1024
Resizing image to max 1024px...
Encoding image...
Generating payload...
Sending request to api.openai.com...
Time spent: 20.79 seconds

=== RETURN CODE ===
0