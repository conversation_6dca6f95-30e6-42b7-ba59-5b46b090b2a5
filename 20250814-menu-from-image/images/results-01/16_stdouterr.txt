=== STDOUT ===
```json
{
  "restaurant": {
    "name": "La Mère Anne",
    "phone": "",
    "address": ""
  },
  "menu": {
    "groups": [
      {
        "name": "Starters",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Cold Starters",
            "articles": [
              {
                "name": "<PERSON>vich<PERSON> van Yellow Fin tonijn",
                "description": "Grannysmith - walnoot - basilicum",
                "price": 10.50,
                "modifiers": []
              },
              {
                "name": "Carpa<PERSON><PERSON>ssen<PERSON>",
                "description": "Parmezaan – pijnboompitten – balsamico",
                "price": 12.50,
                "modifiers": []
              }
            ]
          }
        ]
      },
      {
        "name": "Main Dishes",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Fish",
            "articles": [
              {
                "name": "Thai Kai Kai",
                "description": "Foelie – citroengras – gember – kokosmelk – gerookte paling",
                "price": 24.00,
                "modifiers": []
              }
            ]
          },
          {
            "name": "Meat",
            "articles": [
              {
                "name": "Geroosterde kreeft met little viscreme waarin iets kokos en rode curry",
                "description": "Krab<PERSON>ekjes – kokospeper – salade van mango",
                "price": 28.00,
                "modifiers": []
              }
            ]
          }
        ]
      },
      {
        "name": "Drinks",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Wine",
            "articles": [
              {
                "name": "Wijnarrangement 3 glazen",
                "description": "",
                "price": 18.00,
                "modifiers": []
              }
            ]
          }
        ]
      }
    ]
  },
  "confidence": {
    "articles": 95,
    "articles_problem": "",
    "departments": 92,
    "departments_problem": "",
    "groups": 91,
    "groups_problem": "",
    "vat_rates": 93,
    "vat_rates_problem": "",
    "restaurant_name": 90,
    "restaurant_name_problem": "handwritten",
    "restaurant_phone": 0,
    "restaurant_phone_problem": "not visible",
    "restaurant_address": 0,
    "restaurant_address_problem": "not visible"
  }
}
```

=== STDERR ===
Analyzing image .\16.jpeg...
Model: gpt-4o, Detail: low, Crop: 1024
Resizing image to max 1024px...
Encoding image...
Generating payload...
Sending request to api.openai.com...
Time spent: 25.46 seconds

=== RETURN CODE ===
0