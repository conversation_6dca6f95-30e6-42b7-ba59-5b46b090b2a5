# Prompt

## Text

I have the following menu hierarchy for Dutch restaurant/retail establishments: Group - Department - Article.

- Group specifies which VAT is used

- Dutch VAT Classification:
  - Reduced VAT (9%): Food, non-alcoholic beverages, basic meals
  - Standard VAT (21%): Alcoholic beverages, luxury items

Hierarchy example:

- Starters
  - Cold Starters
    - Gaspacho
    - Cold Salmon
- Main Dishes
  - Burgers
  - Pasta
- Alcohol
  - Long Drinks
  - Beers
  - Vines
- Non Alcohol
  - Coctails
  - Hot Drinks
    - Tea
    - Coffee
  - Cold Drinks
    - Orange Juice
    - Sparkling Water

Instructions:

1. Extract all menu items with names and prices
2. Organize into the hierarchy above
3. Assign Dutch VAT rates as "Reduced" or "Standard" based on item type
4. If multiple prices exist for one item, use first as base price, others as modifiers
5. Provide confidence scores (0-100) for each extraction category
6. If confidence is below 95%, explain the problem (e.g., "blurred", "handwritten", "poor lighting", "unclear text", "not visible)
7. Extract restaurant information, if possible: name, phone, address

Analyze the menu image and provide results in this JSON format:

```json
{
  "restaurant": {
    "name": "Restaurant Name if visible",
    "phone": "Phone if visible",
    "address": "Address if visible"
  },
  "menu": {
    "groups": [
      {
        "name": "Main Dishes",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Burgers",
            "articles": [
              {
                "name": "Classic Cheeseburger",
                "description": "Description if available",
                "price": 15.50,
                "modifiers": [
                  {
                    "name": "Extra cheese",
                    "price": 2.00
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        "name": "Alcohol",
        "vat_rate": "Standard",
        "departments": [
          {
            "name": "Long Drinks",
            "articles": [
              {
                "name": "Gin & Tonic",
                "price": 8.50
              }
            ]
          },
          {
            "name": "Beers",
            "articles": [
              {
                "name": "Heineken",
                "price": 4.50
              }
            ]
          }
        ]
      }
    ]
  },
  "confidence": {
    "articles": 98,
    "articles_problem": "",
    "departments": 95,
    "departments_problem": "",
    "groups": 96,
    "groups_problem": "",
    "vat_rates": 91,
    "vat_rates_problem": "blurred",
    "restaurant_name": 89,
    "restaurant_name_problem": "handwritten",
    "restaurant_phone": 98,
    "restaurant_phone_problem": "",
    "restaurant_address": 90,
    "restaurant_address_problem": "poor lighting"
  }
}
```
