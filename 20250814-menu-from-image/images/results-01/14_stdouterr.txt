=== STDOUT ===
```json
{
  "restaurant": {
    "name": "Unknown",
    "phone": "Unknown",
    "address": "Unknown"
  },
  "menu": {
    "groups": [
      {
        "name": "Starters",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Cold Starters",
            "articles": [
              {
                "name": "Casaer",
                "description": "",
                "price": 12.00,
                "modifiers": []
              },
              {
                "name": "Melanzane",
                "description": "",
                "price": 12.50,
                "modifiers": []
              }
            ]
          }
        ]
      },
      {
        "name": "Main Dishes",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Pasta",
            "articles": [
              {
                "name": "<PERSON>agna",
                "description": "",
                "price": 12.50,
                "modifiers": []
              }
            ]
          }
        ]
      },
      {
        "name": "Alcohol",
        "vat_rate": "Standard",
        "departments": [
          {
            "name": "White Wine",
            "articles": [
              {
                "name": "Sauvignon - France",
                "price": 31.50
              },
              {
                "name": "Chardonnay - USA",
                "price": 15.00
              }
            ]
          }
        ]
      },
      {
        "name": "Non Alcohol",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Soda",
            "articles": [
              {
                "name": "Red Fruits Mix",
                "price": 4.50
              }
            ]
          }
        ]
      }
    ]
  },
  "confidence": {
    "articles": 92,
    "articles_problem": "unclear text",
    "departments": 94,
    "departments_problem": "handwritten",
    "groups": 90,
    "groups_problem": "poor lighting",
    "vat_rates": 93,
    "vat_rates_problem": "blurred",
    "restaurant_name": 50,
    "restaurant_name_problem": "not visible",
    "restaurant_phone": 50,
    "restaurant_phone_problem": "not visible",
    "restaurant_address": 50,
    "restaurant_address_problem": "not visible"
  }
}
```

=== STDERR ===
Analyzing image .\14.jpeg...
Model: gpt-4o, Detail: low, Crop: 1024
Resizing image to max 1024px...
Encoding image...
Generating payload...
Sending request to api.openai.com...
Time spent: 23.16 seconds

=== RETURN CODE ===
0