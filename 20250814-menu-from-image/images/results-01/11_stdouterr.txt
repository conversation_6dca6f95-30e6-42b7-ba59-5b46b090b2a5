=== STDOUT ===
```json
{
  "restaurant": {
    "name": "DARAFT Coffee",
    "phone": "Not visible",
    "address": "Not visible"
  },
  "menu": {
    "groups": [
      {
        "name": "Non Alcohol",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Hot Drinks",
            "articles": [
              {
                "name": "Espresso",
                "price": 2.00,
                "modifiers": [
                  {
                    "name": "Double",
                    "price": 2.30
                  }
                ]
              },
              {
                "name": "Latte",
                "price": 3.50,
                "modifiers": [
                  {
                    "name": "Double",
                    "price": 3.80
                  }
                ]
              },
              {
                "name": "Mocha",
                "price": 3.40,
                "modifiers": [
                  {
                    "name": "Double",
                    "price": 3.70
                  }
                ]
              },
              {
                "name": "Belgian Hot Chocolate",
                "price": 4.50
              },
              {
                "name": "Chai Tea",
                "price": 3.50
              }
            ]
          },
          {
            "name": "Cold Drinks",
            "articles": [
              {
                "name": "Fresh Orange Juice",
                "price": 4.00,
                "modifiers": [
                  {
                    "name": "Large",
                    "price": 5.00
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  },
  "confidence": {
    "articles": 98,
    "articles_problem": "",
    "departments": 96,
    "departments_problem": "",
    "groups": 98,
    "groups_problem": "",
    "vat_rates": 95,
    "vat_rates_problem": "",
    "restaurant_name": 96,
    "restaurant_name_problem": "",
    "restaurant_phone": 0,
    "restaurant_phone_problem": "not visible",
    "restaurant_address": 0,
    "restaurant_address_problem": "not visible"
  }
}
```

=== STDERR ===
Analyzing image .\11.jpeg...
Model: gpt-4o, Detail: low, Crop: 1024
Resizing image to max 1024px...
Encoding image...
Generating payload...
Sending request to api.openai.com...
Time spent: 22.58 seconds

=== RETURN CODE ===
0