=== STDOUT ===
```json
{
  "restaurant": {
    "name": "",
    "phone": "",
    "address": ""
  },
  "menu": {
    "groups": [
      {
        "name": "Main Dishes",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Plats",
            "articles": [
              {
                "name": "Plat du Saumon",
                "description": "",
                "price": 16.5,
                "modifiers": []
              },
              {
                "name": "Betteraves Végé",
                "description": "",
                "price": 16.5,
                "modifiers": []
              },
              {
                "name": "Veggie with Mushrooms",
                "description": "",
                "price": 17,
                "modifiers": []
              },
              {
                "name": "Cabillaud",
                "description": "",
                "price": 18.5,
                "modifiers": []
              },
              {
                "name": "Poulet au Maïs",
                "description": "",
                "price": 22,
                "modifiers": []
              }
            ]
          },
          {
            "name": "Steak Frites",
            "articles": [
              {
                "name": "<PERSON><PERSON><PERSON>",
                "description": "prepared with French fries",
                "price": 21.5,
                "modifiers": []
              }
            ]
          },
          {
            "name": "Salades",
            "articles": [
              {
                "name": "Salade Niçoise",
                "description": "",
                "price": 18,
                "modifiers": []
              },
              {
                "name": "Salade Veggie",
                "description": "",
                "price": 16.5,
                "modifiers": []
              },
              {
                "name": "Salade Lyonnaise",
                "description": "",
                "price": 16.5,
                "modifiers": []
              }
            ]
          }
        ]
      },
      {
        "name": "Supplements",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Supplements",
            "articles": [
              {
                "name": "Le French Frites",
                "description": "",
                "price": 5.5,
                "modifiers": []
              },
              {
                "name": "Purée de Pommes de Terre",
                "description": "",
                "price": 5.5,
                "modifiers": []
              },
              {
                "name": "Chou Rouge",
                "description": "",
                "price": 5.5,
                "modifiers": []
              },
              {
                "name": "Salade Verte",
                "description": "",
                "price": 5.5,
                "modifiers": []
              }
            ]
          }
        ]
      },
      {
        "name": "Desserts",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Desserts et Fromage",
            "articles": [
              {
                "name": "Tarte au Chocolat",
                "description": "",
                "price": 9.5,
                "modifiers": []
              },
              {
                "name": "Tarte Citron",
                "description": "",
                "price": 9.5,
                "modifiers": []
              },
              {
                "name": "Tarte Fruites Rouges",
                "description": "",
                "price": 9.5,
                "modifiers": []
              },
              {
                "name": "Fromage",
                "description": "",
                "price": 9.5,
                "modifiers": []
              }
            ]
          }
        ]
      }
    ]
  },
  "confidence": {
    "articles": 98,
    "articles_problem": "",
    "departments": 96,
    "departments_problem": "",
    "groups": 97,
    "groups_problem": "",
    "vat_rates": 95,
    "vat_rates_problem": "",
    "restaurant_name": 85,
    "restaurant_name_problem": "not visible",
    "restaurant_phone": 85,
    "restaurant_phone_problem": "not visible",
    "restaurant_address": 85,
    "restaurant_address_problem": "not visible"
  }
}
```

=== STDERR ===
Analyzing image .\18.jpeg...
Model: gpt-4o, Detail: low, Crop: 1024
Resizing image to max 1024px...
Encoding image...
Generating payload...
Sending request to api.openai.com...
Time spent: 39.44 seconds

=== RETURN CODE ===
0