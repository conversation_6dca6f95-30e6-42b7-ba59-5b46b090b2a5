=== STDOUT ===
```json
{
  "restaurant": {
    "name": "Unknown",
    "phone": "Unknown",
    "address": "Unknown"
  },
  "menu": {
    "groups": [
      {
        "name": "Non Alcohol",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Hot Drinks",
            "articles": [
              {
                "name": "Espresso",
                "price": 2.25,
                "modifiers": [
                  {
                    "name": "Double",
                    "price": 3.55
                  }
                ]
              },
              {
                "name": "<PERSON>ist<PERSON>to",
                "price": 2.95,
                "modifiers": [
                  {
                    "name": "Double",
                    "price": 3.95
                  }
                ]
              },
              {
                "name": "Cappuccino",
                "price": 3.95,
                "modifiers": [
                  {
                    "name": "Large",
                    "price": 4.50
                  }
                ]
              },
              {
                "name": "Latte",
                "price": 3.95,
                "modifiers": [
                  {
                    "name": "Large",
                    "price": 4.50
                  }
                ]
              },
              {
                "name": "Flat White",
                "price": 3.75
              },
              {
                "name": "Hot Americano",
                "price": 3.25
              },
              {
                "name": "Hot Cocoa",
                "price": 3.95
              },
              {
                "name": "Green Tea",
                "price": 4.25
              },
              {
                "name": "Crazy Chocolate Coffee",
                "price": 5.25
              }
            ]
          }
        ]
      }
    ]
  },
  "confidence": {
    "articles": 95,
    "articles_problem": "slightly unclear text",
    "departments": 98,
    "departments_problem": "",
    "groups": 98,
    "groups_problem": "",
    "vat_rates": 98,
    "vat_rates_problem": "",
    "restaurant_name": 85,
    "restaurant_name_problem": "not visible",
    "restaurant_phone": 85,
    "restaurant_phone_problem": "not visible",
    "restaurant_address": 85,
    "restaurant_address_problem": "not visible"
  }
}
```

=== STDERR ===
Analyzing image .\13.jpeg...
Model: gpt-4o, Detail: low, Crop: 1024
Resizing image to max 1024px...
Encoding image...
Generating payload...
Sending request to api.openai.com...
Time spent: 18.37 seconds

=== RETURN CODE ===
0