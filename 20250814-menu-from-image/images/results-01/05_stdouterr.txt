=== STDOUT ===
```json
{
  "restaurant": {
    "name": "",
    "phone": "",
    "address": ""
  },
  "menu": {
    "groups": [
      {
        "name": "Starters",
        "vat_rate": "Reduced",
        "departments": []
      },
      {
        "name": "Main Dishes",
        "vat_rate": "Reduced",
        "departments": []
      },
      {
        "name": "Non Alcohol",
        "vat_rate": "Reduced",
        "departments": []
      },
      {
        "name": "Desserts",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Pannenkoeken",
            "articles": [
              {
                "name": "Kinder (1 stuk)",
                "price": 4.00
              },
              {
                "name": "Naturel",
                "price": 5.00
              },
              {
                "name": "Suiker",
                "price": 5.50
              },
              {
                "name": "Confituur",
                "price": 6.00
              },
              {
                "name": "Warme Vruchten",
                "price": 6.50
              },
              {
                "name": "Nutella",
                "price": 8.00
              },
              {
                "name": "Banaan",
                "price": 8.00
              },
              {
                "name": "Boune",
                "price": 8.00
              },
              {
                "name": "Warme Krieken",
                "price": 8.70
              },
              {
                "name": "Gekarameliseerde Appeltjes & IJ 3.50",
                "price": 12.50
              },
              {
                "name": "Gekarameliseerde Appeltjes & IJ 3.50",
                "price": 13.70
              }
            ]
          },
          {
            "name": "Brusselse Wafels",
            "articles": [
              {
                "name": "Bloemsuiker",
                "price": 6.50
              },
              {
                "name": "Slagroom",
                "price": 6.80
              },
              {
                "name": "Warme Chocoladesaus",
                "price": 8.50
              },
              {
                "name": "Banaan",
                "price": 8.50
              },
              {
                "name": "Boune",
                "price": 8.50
              },
              {
                "name": "Gekarameliseerde Appeltjes & IJ 3.50",
                "price": 12.50
              }
            ]
          },
          {
            "name": "Overige Desserten",
            "articles": [
              {
                "name": "Milkshake (Smaak naar keuze)",
                "price": 6.00
              },
              {
                "name": "Appelstrudel met IJ",
                "price": 8.00
              },
              {
                "name": "Brownie met IJ",
                "price": 8.00
              }
            ]
          },
          {
            "name": "Toppings naar Keuze",
            "articles": [
              {
                "name": "Slagroom",
                "price": 0.50
              },
              {
                "name": "IJ 3 naar Keuze",
                "price": 1.00
              },
              {
                "name": "Banaan",
                "price": 1.00
              },
              {
                "name": "Karamelsaus",
                "price": 1.00
              },
              {
                "name": "Warme Chocoladesaus",
                "price": 2.00
              },
              {
                "name": "Gekarameliseerde Appeltjes",
                "price": 2.50
              },
              {
                "name": "Warme Krieken",
                "price": 3.00
              }
            ]
          }
        ]
      }
    ]
  },
  "confidence": {
    "articles": 98,
    "articles_problem": "",
    "departments": 98,
    "departments_problem": "",
    "groups": 98,
    "groups_problem": "",
    "vat_rates": 98,
    "vat_rates_problem": "",
    "restaurant_name": 0,
    "restaurant_name_problem": "not visible",
    "restaurant_phone": 0,
    "restaurant_phone_problem": "not visible",
    "restaurant_address": 0,
    "restaurant_address_problem": "not visible"
  }
}
```

=== STDERR ===
Analyzing image .\05.png...
Model: gpt-4o, Detail: low, Crop: 1024
Resizing image to max 1024px...
Encoding image...
Generating payload...
Sending request to api.openai.com...
Time spent: 49.04 seconds

=== RETURN CODE ===
0