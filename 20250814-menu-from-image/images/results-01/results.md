# Menu Processing Results

## Processing Parameters

- **Model(s):** gpt-4o
- **Detail level(s):** low
- **Crop size(s):** 1024px

## Costs

<kbd>![alt text](image.png)</kbd>

---

## Images

### 01.png

<details>
<summary>Image:</summary>

<kbd>![01.png](../01.png)</kbd>

</details>

- Output: [01.json](01.json)
- Time spent: 42.7 seconds

### 02.png

<details>
<summary>Image:</summary>

<kbd>![02.png](../02.png)</kbd>

</details>

- Output: [02.json](02.json)
- Time spent: 33.39 seconds

### 03.png

<details>
<summary>Image:</summary>

<kbd>![03.png](../03.png)</kbd>

</details>

- Output: [03.json](03.json)
- Time spent: 49.3 seconds

### 04.png

<details>
<summary>Image:</summary>

<kbd>![04.png](../04.png)</kbd>

</details>

- Output: [04.json](04.json)
- Time spent: 26.33 seconds

### 05.png

<details>
<summary>Image:</summary>

<kbd>![05.png](../05.png)</kbd>

</details>

- Output: [05.json](05.json)
- Time spent: 49.04 seconds

### 06.png

<details>
<summary>Image:</summary>

<kbd>![06.png](../06.png)</kbd>

</details>

- Output: [06.json](06.json)
- Time spent: 38.95 seconds

### 07.png

<details>
<summary>Image:</summary>

<kbd>![07.png](../07.png)</kbd>

</details>

- Output: [07.json](07.json)
- Time spent: 60.88 seconds

### 08.jpeg

<details>
<summary>Image:</summary>

<kbd>![08.jpeg](../08.jpeg)</kbd>

</details>

- Output: [08.json](08.json)
- Time spent: 35.36 seconds

### 09.jpeg

<details>
<summary>Image:</summary>

<kbd>![09.jpeg](../09.jpeg)</kbd>

</details>

- Output: [09.json](09.json)
- Time spent: 79.85 seconds

### 10.jpeg

<details>
<summary>Image:</summary>

<kbd>![10.jpeg](../10.jpeg)</kbd>

</details>

- Output: [10.json](10.json)
- Time spent: 17.31 seconds

### 11.jpeg

<details>
<summary>Image:</summary>

<kbd>![11.jpeg](../11.jpeg)</kbd>

</details>

- Output: [11.json](11.json)
- Time spent: 22.58 seconds

### 12.jpeg

<details>
<summary>Image:</summary>

<kbd>![12.jpeg](../12.jpeg)</kbd>

</details>

- Output: [12.json](12.json)
- Time spent: 14.65 seconds

### 13.jpeg

<details>
<summary>Image:</summary>

<kbd>![13.jpeg](../13.jpeg)</kbd>

</details>

- Output: [13.json](13.json)
- Time spent: 18.37 seconds

### 14.jpeg

<details>
<summary>Image:</summary>

<kbd>![14.jpeg](../14.jpeg)</kbd>

</details>

- Output: [14.json](14.json)
- Time spent: 23.16 seconds

### 15.jpeg

<details>
<summary>Image:</summary>

<kbd>![15.jpeg](../15.jpeg)</kbd>

</details>

- Output: [15.json](15.json)
- Time spent: 27.82 seconds

### 16.jpeg

<details>
<summary>Image:</summary>

<kbd>![16.jpeg](../16.jpeg)</kbd>

</details>

- Output: [16.json](16.json)
- Time spent: 25.46 seconds

### 17.jpeg

<details>
<summary>Image:</summary>

<kbd>![17.jpeg](../17.jpeg)</kbd>

</details>

- Output: [17.json](17.json)
- Time spent: 30.42 seconds

### 18.jpeg

<details>
<summary>Image:</summary>

<kbd>![18.jpeg](../18.jpeg)</kbd>

</details>

- Output: [18.json](18.json)
- Time spent: 39.44 seconds

### 19.jpeg

<details>
<summary>Image:</summary>

<kbd>![19.jpeg](../19.jpeg)</kbd>

</details>

- Output: [19.json](19.json)
- Time spent: 20.79 seconds

### 20.jpeg

<details>
<summary>Image:</summary>

<kbd>![20.jpeg](../20.jpeg)</kbd>

</details>

- Output: [20.json](20.json)
- Time spent: 25.66 seconds

---

## Time Spent

| Image   | Time Spent (seconds) |
|---------|----------------------|
| 09.jpeg | 79.85                |
| 07.png  | 60.88                |
| 03.png  | 49.3                 |
| 05.png  | 49.04                |
| 01.png  | 42.7                 |
| 18.jpeg | 39.44                |
| 06.png  | 38.95                |
| 08.jpeg | 35.36                |
| 02.png  | 33.39                |
| 17.jpeg | 30.42                |
| 15.jpeg | 27.82                |
| 04.png  | 26.33                |
| 20.jpeg | 25.66                |
| 16.jpeg | 25.46                |
| 14.jpeg | 23.16                |
| 11.jpeg | 22.58                |
| 19.jpeg | 20.79                |
| 13.jpeg | 18.37                |
| 10.jpeg | 17.31                |
| 12.jpeg | 14.65                |

### Statistics

- **Total images processed:** 20
- **Total time spent:** 681.46 seconds
- **Average time per image:** 34.07 seconds
- **Fastest processing:** 14.65 seconds
- **Slowest processing:** 79.85 seconds
