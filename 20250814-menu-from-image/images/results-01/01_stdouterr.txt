=== STDOUT ===
```json
{
  "restaurant": {
    "name": "Unknown",
    "phone": "Unknown",
    "address": "Unknown"
  },
  "menu": {
    "groups": [
      {
        "name": "Lunch",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Main Dishes",
            "articles": [
              {
                "name": "Toscaanse schiacciata",
                "description": "provolone, venkelsalami, pesto, gedroogd tomaatje en sla",
                "price": 13.5
              },
              {
                "name": "Sesambol Korean crispy chicken",
                "description": "zoetzure komkommer, ui en radijs, bosui, sesam en kimchimayonaise",
                "price": 13.5
              },
              {
                "name": "Pita falafel",
                "description": "gegrilde groenten en basilicumveganaise",
                "price": 13.5
              },
              {
                "name": "Broodje buikspek",
                "description": "langzaam gegaard op een sesambrioche, zoetzure wortel- en komkommerlinten, gema<PERSON><PERSON><PERSON> shiitake en srirachamayonaise",
                "price": 13.5
              },
              {
                "name": "Broodje tonijnsalade",
                "description": "augurk, kappert<PERSON><PERSON>, rode ui en chilimayonaise",
                "price": 13.5
              },
              {
                "name": "Club sandwich",
                "description": "gerookte kip, spek, gebakken eitje, tomaat, whiskeysuas en chips",
                "price": 13.5
              },
              {
                "name": "Naanbrood geitenkaas",
                "description": "spinaziepetso, gegrilde groenten, kastanjechampignons, gedroogde tomaat en pecannoten",
                "price": 13.5
              },
              {
                "name": "Ierse scharrelbiefstuk",
                "description": "friet, champignons, sla en rode wijnsaus",
                "price": 24.0
              },
              {
                "name": "Kipsaté",
                "description": "pindasaus, atjar, gebakken uitjes, sla en friet",
                "price": 21.0
              },
              {
                "name": "Kroketten met brood",
                "description": "rund of vega",
                "price": 12.0
              },
              {
                "name": "Uitsmijters en omeletten",
                "description": "vanaf-met biologische scharreleieren van Boer Bas",
                "price": 10.5
              },
              {
                "name": "Soep",
                "description": "",
                "price": 8.5
              },
              {
                "name": "Salade geitenkaas",
                "description": "gegrilde groenten, gedroogde tomaat kastanjechampignons, pecannoten en honing",
                "price": 16.5
              },
              {
                "name": "Saucijzenbroodje",
                "description": "",
                "price": 4.5
              },
              {
                "name": "Poffertjes (15)",
                "description": "",
                "price": 6.0
              }
            ]
          },
          {
            "name": "Tosti's",
            "articles": [
              {
                "name": "Tosti ham/kaas - kaas",
                "description": "",
                "price": 5.0
              },
              {
                "name": "Tosti geitenkaas, appel en honing",
                "description": "",
                "price": 6.5
              },
              {
                "name": "Mexicaanse tosti",
                "description": "pulled chicken, cheddar, jalapeno, salsa en creme fraiche",
                "price": 8.5
              }
            ]
          }
        ]
      }
    ]
  },
  "confidence": {
    "articles": 99,
    "articles_problem": "",
    "departments": 98,
    "departments_problem": "",
    "groups": 99,
    "groups_problem": "",
    "vat_rates": 100,
    "vat_rates_problem": "",
    "restaurant_name": 80,
    "restaurant_name_problem": "not visible",
    "restaurant_phone": 80,
    "restaurant_phone_problem": "not visible",
    "restaurant_address": 80,
    "restaurant_address_problem": "not visible"
  }
}
```

=== STDERR ===
Analyzing image 01.png...
Encoding image...
Generating payload...
Sending request to api.openai.com...
Time spent: 42.70 seconds

=== RETURN CODE ===
0