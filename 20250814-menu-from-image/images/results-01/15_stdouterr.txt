=== STDOUT ===
```json
{
  "restaurant": {
    "name": "BLOEMERS",
    "phone": "",
    "address": ""
  },
  "menu": {
    "groups": [
      {
        "name": "Main Dishes",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Bloemers Lunch",
            "articles": [
              {
                "name": "Avocado Toast",
                "description": "with marinated cherry tomatoes and hummus",
                "price": 11.50,
                "modifiers": []
              },
              {
                "name": "Philly Cheese Steak",
                "description": "with spiced bell peppers, mustard and cheddar cheese",
                "price": 12.50,
                "modifiers": []
              },
              {
                "name": "Bloemers Burger",
                "description": "double smash burger with cheddar cheese, pickle, and fries",
                "price": 15.50,
                "modifiers": [
                  {
                    "name": "Extra cheese",
                    "price": 2.00
                  }
                ]
              }
            ]
          },
          {
            "name": "Croques",
            "articles": [
              {
                "name": "Croque Madame",
                "description": "with bechamel sauce, ham, cheese, and egg",
                "price": 11.50,
                "modifiers": []
              },
              {
                "name": "Croque Vegetarian",
                "description": "with bechamel sauce, mushrooms, and spinach",
                "price": 11.50,
                "modifiers": []
              }
            ]
          }
        ]
      },
      {
        "name": "Non Alcohol",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Soep",
            "articles": [
              {
                "name": "Soep van de dag",
                "description": "Soup of the day",
                "price": 8.50,
                "modifiers": []
              }
            ]
          },
          {
            "name": "Sides",
            "articles": [
              {
                "name": "Pommes Frites",
                "description": "French fries",
                "price": 5.50,
                "modifiers": []
              }
            ]
          }
        ]
      }
    ]
  },
  "confidence": {
    "articles": 97,
    "articles_problem": "",
    "departments": 96,
    "departments_problem": "",
    "groups": 95,
    "groups_problem": "",
    "vat_rates": 90,
    "vat_rates_problem": "blurred",
    "restaurant_name": 94,
    "restaurant_name_problem": "logo partially unclear",
    "restaurant_phone": 0,
    "restaurant_phone_problem": "not visible",
    "restaurant_address": 0,
    "restaurant_address_problem": "not visible"
  }
}
```

=== STDERR ===
Analyzing image .\15.jpeg...
Model: gpt-4o, Detail: low, Crop: 1024
Resizing image to max 1024px...
Encoding image...
Generating payload...
Sending request to api.openai.com...
Time spent: 27.82 seconds

=== RETURN CODE ===
0