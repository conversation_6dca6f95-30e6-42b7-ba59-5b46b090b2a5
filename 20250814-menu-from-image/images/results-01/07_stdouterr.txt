=== STDOUT ===
```json
{
  "restaurant": {
    "name": "",
    "phone": "",
    "address": ""
  },
  "menu": {
    "groups": [
      {
        "name": "Main Dishes",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Diner",
            "articles": [
              {
                "name": "Kipsaté - pindasaus, atjar, gebakken uitjes",
                "description": "",
                "price": 21.00,
                "modifiers": []
              },
              {
                "name": "He<PERSON>l filet - rozemarijn a<PERSON>appelt<PERSON>, gegrilde groenten en een dilemmayo",
                "description": "",
                "price": 26.50,
                "modifiers": []
              },
              {
                "name": "Ierse scharrelbiefstuk - friet, champignons, sla en jus de veau",
                "description": "",
                "price": 21.50,
                "modifiers": []
              },
              {
                "name": "Vegetarische kipburger - op een brioche broodje, tomaat, avocado, sla, jalapeno, tomaten salsa en cheddar",
                "description": "",
                "price": 23.00,
                "modifiers": []
              },
              {
                "name": "Gekonfijte eendenbout - krieltjes uit de oven, gegrilde groenten en jus de veau",
                "description": "",
                "price": 24.00,
                "modifiers": []
              },
              {
                "name": "Oosters gemarineerde spareribs - friet, sla en knoflooksaus",
                "description": "",
                "price": 24.50,
                "modifiers": []
              },
              {
                "name": "Salade gegrilde kip - avocado, komkommer, cherrytomaten, champignons en honing",
                "description": "",
                "price": 18.00,
                "modifiers": []
              }
            ]
          }
        ]
      },
      {
        "name": "Desserts",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Dessert",
            "articles": [
              {
                "name": "Chocolade lava taartje met slagroom en frambozen saus",
                "description": "",
                "price": 9.00,
                "modifiers": []
              }
            ]
          }
        ]
      },
      {
        "name": "Snacks",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Snacks",
            "articles": [
              {
                "name": "Kipsaté",
                "description": "",
                "price": 11.00,
                "modifiers": []
              },
              {
                "name": "Vegetarische bitterballen (8)",
                "description": "",
                "price": 9.00,
                "modifiers": []
              },
              {
                "name": "Kaasengeltjes (8)",
                "description": "",
                "price": 9.00,
                "modifiers": []
              },
              {
                "name": "Gouda kaas",
                "description": "",
                "price": 8.00,
                "modifiers": []
              },
              {
                "name": "Bittergarnituur (15)",
                "description": "Kan ook vega",
                "price": 22.00,
                "modifiers": []
              },
              {
                "name": "Gedroogde worst - augurk en Amsterdamse ui",
                "description": "",
                "price": 10.00,
                "modifiers": []
              },
              {
                "name": "Nacho’s - kaas, salsa, jalapeno en creme fraiche",
                "description": "",
                "price": 10.00,
                "modifiers": []
              },
              {
                "name": "Gyoza’s met saus",
                "description": "",
                "price": 9.00,
                "modifiers": []
              },
              {
                "name": "Tomatensoep",
                "description": "",
                "price": 8.00,
                "modifiers": []
              },
              {
                "name": "Truffel-mayonnaise en Parmezaanse kaas",
                "description": "",
                "price": 9.00,
                "modifiers": []
              },
              {
                "name": "Brood met smeersels",
                "description": "",
                "price": 8.00,
                "modifiers": []
              },
              {
                "name": "Olijven",
                "description": "",
                "price": 4.50,
                "modifiers": []
              }
            ]
          }
        ]
      }
    ]
  },
  "confidence": {
    "articles": 98,
    "articles_problem": "",
    "departments": 98,
    "departments_problem": "",
    "groups": 98,
    "groups_problem": "",
    "vat_rates": 98,
    "vat_rates_problem": "",
    "restaurant_name": 0,
    "restaurant_name_problem": "not visible",
    "restaurant_phone": 0,
    "restaurant_phone_problem": "not visible",
    "restaurant_address": 0,
    "restaurant_address_problem": "not visible"
  }
}
```

=== STDERR ===
Analyzing image .\07.png...
Model: gpt-4o, Detail: low, Crop: 1024
Resizing image to max 1024px...
Encoding image...
Generating payload...
Sending request to api.openai.com...
Time spent: 60.88 seconds

=== RETURN CODE ===
0