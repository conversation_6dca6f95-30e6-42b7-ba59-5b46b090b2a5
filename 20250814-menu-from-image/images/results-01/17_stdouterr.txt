=== STDOUT ===
```json
{
  "restaurant": {
    "name": "Not visible",
    "phone": "Not visible",
    "address": "Not visible"
  },
  "menu": {
    "groups": [
      {
        "name": "Starters",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Entrées",
            "articles": [
              {
                "name": "Description not clear",
                "price": "Not discernible"
              }
            ]
          }
        ]
      },
      {
        "name": "Main Dishes",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Petit Plats",
            "articles": [
              {
                "name": "Description not clear",
                "price": "Not discernible"
              }
            ]
          },
          {
            "name": "Sandwiches",
            "articles": [
              {
                "name": "Description not clear",
                "price": "Not discernible"
              }
            ]
          },
          {
            "name": "Steak Frites",
            "articles": [
              {
                "name": "Description not clear",
                "price": "Not discernible"
              }
            ]
          }
        ]
      },
      {
        "name": "Non Alcohol",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Jus de Fruit ou Boisson Fraîche",
            "articles": [
              {
                "name": "Description not clear",
                "price": "Not discernible"
              }
            ]
          }
        ]
      }
    ]
  },
  "confidence": {
    "articles": 75,
    "articles_problem": "Unclear text",
    "departments": 80,
    "departments_problem": "Unclear text",
    "groups": 85,
    "groups_problem": "Unclear text",
    "vat_rates": 90,
    "vat_rates_problem": "Not visible",
    "restaurant_name": 50,
    "restaurant_name_problem": "Not visible",
    "restaurant_phone": 50,
    "restaurant_phone_problem": "Not visible",
    "restaurant_address": 50,
    "restaurant_address_problem": "Not visible"
  }
}
```

=== STDERR ===
Analyzing image .\17.jpeg...
Model: gpt-4o, Detail: low, Crop: 1024
Resizing image to max 1024px...
Encoding image...
Generating payload...
Sending request to api.openai.com...
Time spent: 30.42 seconds

=== RETURN CODE ===
0