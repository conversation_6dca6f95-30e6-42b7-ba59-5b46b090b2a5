# Feedback from Nikita

## 01.png

AI recognised this as ‘Main dishes’ department, but I do think that the client would prefer to have ‘Lunch’ here as a department. Looking at their menu and keeping in mind how other clients build their menus, they would want ‘Lunch’ to be their department.

Now the AI tool counts ‘Lunch’ is as a group. I think it’s too narrow. From what I have seen at the client’s menus, quite often they use just 3 groups: Food (9% VAT - AI got it right here by the way), Alcoholic drinks (21%), Non-alcoholic drinks (9%). I think we can go this way in general and make 3 groups like that by default.

I see that AI also picks up the descriptions for these articles. Since we don’t have descriptions for articles in the unTill Air backoffice, then I think it makes sense to leave it out. This way I assume the capacity of the tool would go up.

## 07.png

Here, the tool recognises <PERSON><PERSON> as a department - good. But it defines ‘Main dishes’ as a group. I would prefer to see just ‘Food’ there.

At this picture, the tool picks up the article itself together with its description and puts it all in the article name as far as I can see. This is not optimal. Instead of ‘Ierse scharrelbiefstuk - friet, champignons, sla en jus de veau’ it should be just ‘Ierse scharrelbiefstuk’

For desserts, it makes a separate group - Desserts. It doesn’t make a majour sense. Let’s stick to the group ‘Food’.

Kipsaté is moved to Snacks but it has to be under ‘Diner’

Bittergarnituur - a note next to this article says that they can also make it vega. Ideally, it would trigger a mandatory choice modifier: Standard or Vega. But it’s not a problem if it doesn’t.

Chocolade lava taartje met slagroom en frambozen saus → Chocolade lava taartje

I couldn’t find a couple of articles from the menu in the AI response: e.g. Friet, Mini frikandellen.

The tool mentions ‘Gouda kaas’ but I think the client would prefer to see Delfst Goud extra oude kaas instead. Also the price is not right: the tool shows 8 EUR but it has to be 10 EUR accordign to the menu.

The price of Brood met smeersels is 8,5 EUR, not 8 EUR

## Prompt improvements

- https://app.augmentcode.com/share/n0VOGGD3Baw
