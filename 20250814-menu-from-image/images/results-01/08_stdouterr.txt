=== STDOUT ===
```json
{
  "restaurant": {
    "name": "Blackbird Coffee & Vintage",
    "phone": "",
    "address": ""
  },
  "menu": {
    "groups": [
      {
        "name": "Non Alcohol",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Hot Drinks",
            "articles": [
              {
                "name": "Espresso",
                "price": 2.00
              },
              {
                "name": "Americano",
                "price": 2.50
              },
              {
                "name": "Filter V60",
                "price": 2.50
              },
              {
                "name": "Extra Shot",
                "price": 0.50
              },
              {
                "name": "Espresso Macchiato",
                "price": 2.25
              },
              {
                "name": "Cappuccino",
                "price": 2.75
              },
              {
                "name": "Flat White",
                "price": 3.00
              },
              {
                "name": "Latte Macchiato",
                "price": 3.00
              },
              {
                "name": "Oat/Soy/Coconut Milk",
                "price": 0.30
              },
              {
                "name": "Tea",
                "price": 2.50
              },
              {
                "name": "Fresh Mint Tea",
                "price": 2.90
              },
              {
                "name": "Chai Latte",
                "price": 3.00
              },
              {
                "name": "Fresh Ginger Tea",
                "price": 2.90
              }
            ]
          },
          {
            "name": "Cold Drinks",
            "articles": [
              {
                "name": "Soft Drinks",
                "price": 2.50
              },
              {
                "name": "Orange Juice",
                "price": 3.00
              },
              {
                "name": "Blender Juice 3.0",
                "price": 3.50
              }
            ]
          }
        ]
      },
      {
        "name": "Food",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Breakfast",
            "articles": [
              {
                "name": "Croissant",
                "price": 2.00
              },
              {
                "name": "Croissant Cheese",
                "price": 2.50
              },
              {
                "name": "Croissant Jam",
                "price": 2.80
              },
              {
                "name": "Yoghurt/Granola",
                "price": 2.80
              }
            ]
          },
          {
            "name": "Pastries",
            "articles": [
              {
                "name": "Almond Cookies",
                "price": 1.50
              },
              {
                "name": "Lemon Cake",
                "price": 3.00
              },
              {
                "name": "Pastel de Nata",
                "price": 2.50
              }
            ]
          }
        ]
      }
    ]
  },
  "confidence": {
    "articles": 98,
    "articles_problem": "",
    "departments": 98,
    "departments_problem": "",
    "groups": 98,
    "groups_problem": "",
    "vat_rates": 98,
    "vat_rates_problem": "",
    "restaurant_name": 95,
    "restaurant_name_problem": "",
    "restaurant_phone": 0,
    "restaurant_phone_problem": "not visible",
    "restaurant_address": 0,
    "restaurant_address_problem": "not visible"
  }
}
```

=== STDERR ===
Analyzing image .\08.jpeg...
Model: gpt-4o, Detail: low, Crop: 1024
Resizing image to max 1024px...
Encoding image...
Generating payload...
Sending request to api.openai.com...
Time spent: 35.36 seconds

=== RETURN CODE ===
0