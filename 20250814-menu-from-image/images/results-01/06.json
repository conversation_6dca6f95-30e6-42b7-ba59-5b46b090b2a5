{"restaurant": {"name": "", "phone": "", "address": "@cozydelft"}, "menu": {"groups": [{"name": "Starters", "vat_rate": "Reduced", "departments": [{"name": "All-Day Breakfast", "articles": [{"name": "Good Morning Delft", "description": "croissant with jam or butter | orange juice | coffee of choice", "price": 9.0}, {"name": "Yoghurt Bowl", "description": "Greek yoghurt | granola | fresh fruit | chia seeds | plant-based yoghurt +2", "price": 8.0, "modifiers": [{"name": "Plant-based yoghurt", "price": 2.0}]}, {"name": "Brioche Na Chapa", "description": "Brazilian toasted brioche with pan-toasted cheese spread & mozzarella cheese", "price": 9.0}, {"name": "Brioche Na Chapa Deluxe", "description": "Brazilian toasted brioche with pan-toasted cheese spread, all Brazilian toppings", "price": 12.0}, {"name": "Pão De Queijo", "description": "Brazilian cheese balls (3 pieces) | side of spread | highly baked (15min)", "price": 6.0}]}]}, {"name": "Main Dishes", "vat_rate": "Reduced", "departments": [{"name": "<PERSON><PERSON><PERSON>'s and Brioches", "articles": [{"name": "Classic To<PERSON>i", "description": "sourdough bread | old cheese | ham | ketchup", "price": 8.0}, {"name": "Fresh Brioche", "description": "brioche | old cheese | pulled sauce", "price": 9.0}, {"name": "Special Brioche", "description": "brioche | old cheese | spicy pulled sauce", "price": 10.0}, {"name": "<PERSON><PERSON>", "description": "brioche | old cheese | pulled sauce | avocado | bacon | Japanese mayonnaise", "price": 14.0}, {"name": "Pulled Pork Brioche", "description": "brioche | slow-cooked pulled pork | old cheese", "price": 14.0, "modifiers": [{"name": "Gluten-free bread", "price": 1.5}]}]}, {"name": "Eggs", "articles": [{"name": "Fresh Eggs", "description": "2 scrambled eggs or omelette | spinach | tomato | cucumber | avocado | bacon | smoked salmon | cheese | pear powder | bread", "price": 14.0}, {"name": "Classic Eggs", "description": "4 scrambled eggs or omelette | spinach | avocado | bacon | pear powder | bread", "price": 14.0}]}]}]}, "confidence": {"articles": 99, "articles_problem": "", "departments": 99, "departments_problem": "", "groups": 99, "groups_problem": "", "vat_rates": 100, "vat_rates_problem": "", "restaurant_name": 0, "restaurant_name_problem": "not visible", "restaurant_phone": 0, "restaurant_phone_problem": "not visible", "restaurant_address": 95, "restaurant_address_problem": "partial extraction based on social media handle"}}