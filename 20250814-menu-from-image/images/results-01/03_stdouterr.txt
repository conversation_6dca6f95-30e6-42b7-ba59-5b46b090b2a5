=== STDOUT ===
```json
{
  "restaurant": {
    "name": "",
    "phone": "",
    "address": ""
  },
  "menu": {
    "groups": [
      {
        "name": "Non Alcohol",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "<PERSON><PERSON><PERSON>",
            "articles": [
              {
                "name": "Espresso",
                "price": 2.70
              },
              {
                "name": "Dubbele Espresso",
                "price": 3.00
              },
              {
                "name": "Espresso Macchiato",
                "price": 3.00
              },
              {
                "name": "Lungo",
                "price": 2.70
              },
              {
                "name": "Déca",
                "price": 2.80
              },
              {
                "name": "Cappuccino Melkschuim",
                "price": 3.50
              },
              {
                "name": "Cappuccino Slagroom",
                "price": 3.50
              },
              {
                "name": "Cappuccino Tiramisu",
                "price": 5.00
              },
              {
                "name": "Cappuccino Karamel",
                "price": 5.00
              },
              {
                "name": "Cappuccino Nutella",
                "price": 5.00
              },
              {
                "name": "Latte Macchiato",
                "price": 4.00
              },
              {
                "name": "Karamel Latte",
                "price": 4.60
              },
              {
                "name": "Vanille Latte",
                "price": 4.60
              },
              {
                "name": "Speculatte",
                "price": 4.60
              },
              {
                "name": "Cheesecake Latte",
                "price": 6.00
              },
              {
                "name": "Creme Brûlée Latte",
                "price": 6.00
              },
              {
                "name": "Irish Coffee (whisky)",
                "price": 8.20
              },
              {
                "name": "Baileys Coffee (baileys)",
                "price": 8.20
              },
              {
                "name": "Italian Coffee (amaretto)",
                "price": 8.20
              }
            ]
          },
          {
            "name": "Thee",
            "articles": [
              {
                "name": "Zwarte Thee",
                "price": 3.00
              },
              {
                "name": "Kamille Thee",
                "price": 3.00
              },
              {
                "name": "Groene Thee",
                "price": 3.00
              },
              {
                "name": "Rooibos Thee",
                "price": 3.00
              },
              {
                "name": "Wilde Bessen Thee",
                "price": 3.00
              },
              {
                "name": "Verse Munt",
                "price": 4.00
              }
            ]
          },
          {
            "name": "Chocomelk",
            "articles": [
              {
                "name": "Warme Chocomelk",
                "description": "(melk-, fondant- of witte chocolate)",
                "price": 3.50
              },
              {
                "name": "Warme Choco Bueno",
                "price": 4.50
              },
              {
                "name": "Warme Melk",
                "description": "(zonder extra)",
                "price": 2.90
              }
            ]
          },
          {
            "name": "Affogato",
            "articles": [
              {
                "name": "Classic",
                "description": "(vanille ijs en espresso)",
                "price": 5.75
              },
              {
                "name": "Mocha",
                "description": "(chocolade ijs en espresso)",
                "price": 6.30
              },
              {
                "name": "Nutty-Caramel",
                "description": "(classic + karamel + nootjes)",
                "price": 6.30
              }
            ]
          },
          {
            "name": "Supplementen",
            "articles": [
              {
                "name": "Slagroom",
                "price": 1.50
              },
              {
                "name": "Plantaardige Melk (haver)",
                "price": 0.60
              },
              {
                "name": "Décafé",
                "price": 0.10
              }
            ]
          },
          {
            "name": "Verwenkoffie",
            "articles": [
              {
                "name": "Assortiment NICE! zoetigheden",
                "price": 6.00
              }
            ]
          }
        ]
      }
    ]
  },
  "confidence": {
    "articles": 98,
    "articles_problem": "",
    "departments": 98,
    "departments_problem": "",
    "groups": 98,
    "groups_problem": "",
    "vat_rates": 100,
    "vat_rates_problem": "",
    "restaurant_name": 0,
    "restaurant_name_problem": "not visible",
    "restaurant_phone": 0,
    "restaurant_phone_problem": "not visible",
    "restaurant_address": 0,
    "restaurant_address_problem": "not visible"
  }
}
```

=== STDERR ===
Analyzing image 03.png...
Encoding image...
Generating payload...
Sending request to api.openai.com...
Time spent: 49.30 seconds

=== RETURN CODE ===
0