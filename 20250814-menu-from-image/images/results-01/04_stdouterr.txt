=== STDOUT ===
```json
{
  "restaurant": {
    "name": "Unknown",
    "phone": "Unknown",
    "address": "Unknown"
  },
  "menu": {
    "groups": [
      {
        "name": "On the Bread",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Main Courses",
            "articles": [
              {
                "name": "Egg Avocado",
                "description": "sourdough bread | garlic butter | sliced avocado | 2 poached eggs | baked tomatoes | chive oil",
                "price": 16.00,
                "modifiers": [
                  {
                    "name": "Bacon",
                    "price": 3.00
                  },
                  {
                    "name": "Smoked Salmon",
                    "price": 5.00
                  }
                ]
              },
              {
                "name": "Citric Avocado",
                "description": "focaccia | avocado-lime spread | dill | tomatoes | sweet & sour cucumber",
                "price": 14.00,
                "modifiers": [
                  {
                    "name": "Smoked Salmon",
                    "price": 5.00
                  }
                ]
              },
              {
                "name": "Pulled Mushroom",
                "description": "sourdough bread | pulled mushroom | homemade barbecue sauce | sweet & sour carrot | crispy onions",
                "price": 16.00
              },
              {
                "name": "Savoury French Toast",
                "description": "brioche | smoked salmon | dill mayonnaise | wasabi crumble | sweet & sour cucumber | avocado drops",
                "price": 16.00
              }
            ]
          }
        ]
      },
      {
        "name": "Sweet Delights",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Desserts",
            "articles": [
              {
                "name": "Homemade Poffertjes",
                "description": "10 pieces | fresh fruit | powdered sugar | butter",
                "price": 9.50
              },
              {
                "name": "French Toast",
                "description": "classic with fresh fruit & maple syrup or crème brûlée",
                "price": 12.00
              }
            ]
          }
        ]
      },
      {
        "name": "Add-ons",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Extras",
            "articles": [
              {
                "name": "Bread",
                "price": 2.00
              },
              {
                "name": "Nutella",
                "price": 2.00
              },
              {
                "name": "Cheese",
                "price": 2.00
              },
              {
                "name": "Ham",
                "price": 2.00
              },
              {
                "name": "Bacon",
                "price": 3.00
              },
              {
                "name": "Fried or Poached Egg",
                "price": 3.00
              },
              {
                "name": "Avocado",
                "price": 4.00
              },
              {
                "name": "Smoked Salmon",
                "price": 5.00
              }
            ]
          }
        ]
      }
    ]
  },
  "confidence": {
    "articles": 98,
    "articles_problem": "",
    "departments": 98,
    "departments_problem": "",
    "groups": 98,
    "groups_problem": "",
    "vat_rates": 99,
    "vat_rates_problem": "",
    "restaurant_name": 50,
    "restaurant_name_problem": "not visible",
    "restaurant_phone": 50,
    "restaurant_phone_problem": "not visible",
    "restaurant_address": 50,
    "restaurant_address_problem": "not visible"
  }
}
```

=== STDERR ===
Analyzing image 04.png...
Encoding image...
Generating payload...
Sending request to api.openai.com...
Time spent: 26.33 seconds

=== RETURN CODE ===
0