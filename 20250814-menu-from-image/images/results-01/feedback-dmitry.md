# Feedback Dmitry

## Summary of Stage 1 Research: Menu Picture to Articles*

1. **VAT**

   * Overall results were very good.
   * Some mistakes occurred with articles containing alcohol (e.g., Irish Coffee).
   * Results could likely be improved by refining the prompt.

2. **Groups**

   * <PERSON><PERSON> suggested predefined groups, which seems like a perfect idea:

     * Food
     * Non-alcoholic drinks
     * Alcoholic drinks
   * Let’s adapt the prompt accordingly and validate the results.

3. **Departments**

   * A potential solution is to group articles by **sections in the menu** and name departments after these section titles.
   * Usually, sections are easy to distinguish from articles (different font).
   * Let’s adjust the prompt for this approach and check the outcome.

4. **Articles**

   * <PERSON><PERSON> suggested recognizing modifiers separately from articles.
   * For now, I believe it’s better to leave modifier recognition for a later stage.
   * Regarding article names:

     * Limit names to **max 5 words**, but check for duplicates and extend when necessary.
     * Use AI tools for translations.
   * AI struggles with complex menus (e.g., two price columns or combo menus).
   * Suggestion: simplify recognition by treating everything in a section as articles and using the **first identifiable price**.

5. **Time**

   * Recognition takes **30–40 seconds**.
   * Customers should see a **progress bar or similar indicator** during processing.

6. **Recognition Results**

   * Issues observed: wrong section assignment, incorrect prices, or missed items (e.g., Example 8: Cortado, Latte).
   * Location owners must have an **easy way to correct mistakes** with bulk-edit options.
   * Current confidence scores aren’t reliable (Example 8: 12 mistakes for 27 articles but 98% confidence).
   * Alternative approach: rework how confidence is calculated or highlight articles that need manual review.

7. **Re-recognition**

   * Example 14 showed completely incorrect recognition.
   * Customers should have an **easy way to re-run recognition**.

8. **Digital Menus vs. Photos**

   * Recognition quality is significantly better for **digital menus** compared to photos.
   * Best practice: first ask customers to enter a **menu page link** or upload **screenshots** rather than photos.
   * Let’s also test recognition via menu links.

---

## Prompt

**You are a meticulous menu OCR.**
Given the attached menu photo, extract every purchasable item with **only**:

* `name`: **copy the text exactly as printed** (preserve capitalization, punctuation, accents, symbols, and hyphenation; do not translate, normalize, or rephrase).
* `price`: **copy exactly as printed** (currency symbol placement, separators like `,` or `.`, ranges, “MP/Market Price”, etc.). If no price is visible, use `null`.

Return **JSON only** in the schema below.

### Instructions

1. Scan the entire image (all columns/sections).
2. For each unique purchasable line:

   * `name`: exact text as printed.
   * `price`: exact text as printed or `null` if you have any kind of uncertainty.
   * `confidence` (0–100): your self-assessed recognition confidence for that line, using this rubric:

     * 100 = perfectly clear, no ambiguity
     * 90–99 = tiny uncertainty (one character or digit)
     * 70–89 = noticeable uncertainty (blur/glare/stylized font)
     * 40–69 = high uncertainty (partially obstructed)
     * 0–39 = unreadable/guessing
3. Duplicates:

   * If the **same** line (identical name and price) appears more than once, keep **one** entry.
   * If the **same name** appears with **different prices** (e.g., sizes), create **separate entries**, each with that exact `name` and its corresponding `price` as printed.
4. Reading order: return items in top-to-bottom, left-to-right order.
5. Aggregates:

   * `total_items` = number of items returned
   * `low_conf_items` = count where `confidence < 100`
   * `low_conf_pct` = round(100 \* low\_conf\_items / total\_items, 1)
6. **No hallucinations.** If any part is missing/uncertain, lower `confidence` accordingly.
7. Output **JSON only**—no extra prose.

Output schema:
```json
{
  "items": [
    {
      "id": "string-unique",
      "name": "string (exactly as printed)",
      "price": "string exactly as printed or null",
      "confidence": number
    }
  ],
  "summary": {
    "total_items": number,
    "low_conf_items": number,
    "low_conf_pct": number
  },
  "low_confidence_item_ids": ["id", "..."]
}
```

Example (illustrative):
```json
{
  "items": [
    {
      "id": "i001",
      "name": "Margherita",
      "price": "€8,50",
      "confidence": 100
    },
    {
      "id": "i002",
      "name": "Pepperoni",
      "price": "€9.90",
      "confidence": 95
    },
    {
      "id": "i003",
      "name": "Pepperoni",
      "price": "€12.50 (Large)",
      "confidence": 92
    },
    {
      "id": "i004",
      "name": "Chef's Special – Truffle",
      "price": null,
      "confidence": 70
    }
  ],
  "summary": {
    "total_items": 4,
    "low_conf_items": 3,
    "low_conf_pct": 75.0
  },
  "low_confidence_item_ids": ["i002", "i003", "i004"]
}
```

### Results ../08.jpeg

- ![alt text](../08.jpeg)
- [results-01](08.json)

#### 4o

```json
{
    "items": [
        {
            "id": "i001",
            "name": "espresso",
            "price": "2.00",
            "confidence": 100
        },
        {
            "id": "i002",
            "name": "americano",
            "price": "2.25",
            "confidence": 100
        },
        {
            "id": "i003",
            "name": "filter v60",
            "price": "3.00",
            "confidence": 100
        },
        {
            "id": "i004",
            "name": "extra shot",
            "price": "0.50",
            "confidence": 100
        },
        {
            "id": "i005",
            "name": "espresso macchiato",
            "price": "2.25",
            "confidence": 100
        },
        {
            "id": "i006",
            "name": "cortado",
            "price": "2.50",
            "confidence": 100
        },
        {
            "id": "i007",
            "name": "cappuccino",
            "price": "2.75",
            "confidence": 100
        },
        {
            "id": "i008",
            "name": "latte",
            "price": "2.75",
            "confidence": 100
        },
        {
            "id": "i009",
            "name": "latte macchiato",
            "price": "3.00",
            "confidence": 100
        },
        {
            "id": "i010",
            "name": "flat white",
            "price": "3.00",
            "confidence": 100
        },
        {
            "id": "i011",
            "name": "oat/soy/coconut milk",
            "price": "0.25",
            "confidence": 100
        },
        {
            "id": "i012",
            "name": "tea",
            "price": "2.50",
            "confidence": 100
        },
        {
            "id": "i013",
            "name": "ginger tea",
            "price": "2.75",
            "confidence": 100
        },
        {
            "id": "i014",
            "name": "mint tea",
            "price": "2.75",
            "confidence": 100
        },
        {
            "id": "i015",
            "name": "choco latte",
            "price": "3.00",
            "confidence": 100
        },
        {
            "id": "i016",
            "name": "chai latte",
            "price": "3.00",
            "confidence": 100
        },
        {
            "id": "i017",
            "name": "dirty chai",
            "price": "4.00",
            "confidence": 100
        },
        {
            "id": "i018",
            "name": "mocha",
            "price": "4.00",
            "confidence": 100
        },
        {
            "id": "i019",
            "name": "croissant",
            "price": "2.00",
            "confidence": 100
        },
        {
            "id": "i020",
            "name": "croissant cheese",
            "price": "2.50",
            "confidence": 100
        },
        {
            "id": "i021",
            "name": "croissant jam",
            "price": "2.50",
            "confidence": 100
        },
        {
            "id": "i022",
            "name": "yoghurt granola",
            "price": "4.00",
            "confidence": 100
        },
        {
            "id": "i023",
            "name": "almond cookies",
            "price": "1.75",
            "confidence": 100
        },
        {
            "id": "i024",
            "name": "pastel de nata",
            "price": "2.50",
            "confidence": 100
        },
        {
            "id": "i025",
            "name": "soft drinks",
            "price": "2.50",
            "confidence": 100
        },
        {
            "id": "i026",
            "name": "orange juice",
            "price": "3.00",
            "confidence": 100
        },
        {
            "id": "i027",
            "name": "blender juice",
            "price": "3.50",
            "confidence": 100
        }
    ],
    "summary": {
        "total_items": 27,
        "low_conf_items": 0,
        "low_conf_pct": 0
    },
    "low_confidence_item_ids": []
}
```

#### o5 thinking

```json
{
    "items": [
        {
            "id": "i001",
            "name": "espresso",
            "price": "2.00",
            "confidence": 100
        },
        {
            "id": "i002",
            "name": "americano",
            "price": "2.25",
            "confidence": 100
        },
        {
            "id": "i003",
            "name": "filter v60",
            "price": "3.00",
            "confidence": 100
        },
        {
            "id": "i004",
            "name": "extra shot",
            "price": "0.50",
            "confidence": 100
        },
        {
            "id": "i005",
            "name": "espresso macchiato",
            "price": "2.25",
            "confidence": 95
        },
        {
            "id": "i006",
            "name": "cortado",
            "price": "2.25",
            "confidence": 100
        },
        {
            "id": "i007",
            "name": "cappuccino",
            "price": "2.50",
            "confidence": 100
        },
        {
            "id": "i008",
            "name": "latte",
            "price": "2.75",
            "confidence": 100
        },
        {
            "id": "i009",
            "name": "latte macchiato",
            "price": "3.00",
            "confidence": 100
        },
        {
            "id": "i010",
            "name": "flat white",
            "price": "3.00",
            "confidence": 100
        },
        {
            "id": "i011",
            "name": "oat/soy/coconut milk",
            "price": "0.25",
            "confidence": 98
        },
        {
            "id": "i012",
            "name": "tea",
            "price": "2.50",
            "confidence": 100
        },
        {
            "id": "i013",
            "name": "ginger tea",
            "price": "2.75",
            "confidence": 100
        },
        {
            "id": "i014",
            "name": "mint tea",
            "price": "2.75",
            "confidence": 100
        },
        {
            "id": "i015",
            "name": "choco latte",
            "price": "3.00",
            "confidence": 98
        },
        {
            "id": "i016",
            "name": "chai latte",
            "price": "3.00",
            "confidence": 100
        },
        {
            "id": "i017",
            "name": "dirty chai",
            "price": "4.00",
            "confidence": 100
        },
        {
            "id": "i018",
            "name": "mocha",
            "price": "4.00",
            "confidence": 100
        },
        {
            "id": "i019",
            "name": "croissant",
            "price": "2.00",
            "confidence": 100
        },
        {
            "id": "i020",
            "name": "croissant cheese",
            "price": "2.50",
            "confidence": 100
        },
        {
            "id": "i021",
            "name": "croissant jam",
            "price": "2.50",
            "confidence": 100
        },
        {
            "id": "i022",
            "name": "yoghurt granola",
            "price": "4.00",
            "confidence": 100
        },
        {
            "id": "i023",
            "name": "almond cookies",
            "price": "1.75",
            "confidence": 100
        },
        {
            "id": "i024",
            "name": "pastel de nata",
            "price": "2.50",
            "confidence": 100
        },
        {
            "id": "i025",
            "name": "soft drinks",
            "price": "2.50",
            "confidence": 100
        },
        {
            "id": "i026",
            "name": "orange juice",
            "price": "3.00",
            "confidence": 100
        },
        {
            "id": "i027",
            "name": "blender juice",
            "price": "3.50",
            "confidence": 100
        }
    ],
    "summary": {
        "total_items": 27,
        "low_conf_items": 3,
        "low_conf_pct": 11.1
    },
    "low_confidence_item_ids": [
        "i005",
        "i011",
        "i015"
    ]
}
```

#### Comparison (Augment Code)

Looking at the prices from the OCR results vs the structured JSON output in `08.json`:

| Article              | OCR 4o Price | OCR o5 Price | 08.json Price | Difference (4o vs JSON) | Difference (o5 vs JSON) |
|----------------------|--------------|--------------|---------------|-------------------------|-------------------------|
| espresso             | 2.00         | 2.00         | 2.00          | ✓ Match                 | ✓ Match                 |
| americano            | 2.25         | 2.25         | 2.50          | +0.25                   | +0.25                   |
| filter v60           | 3.00         | 3.00         | 2.50          | -0.50                   | -0.50                   |
| extra shot           | 0.50         | 0.50         | 0.50          | ✓ Match                 | ✓ Match                 |
| espresso macchiato   | 2.25         | 2.25         | 2.25          | ✓ Match                 | ✓ Match                 |
| cortado              | 2.50         | 2.25         | **Missing**   | N/A                     | N/A                     |
| cappuccino           | 2.75         | 2.50         | 2.75          | ✓ Match                 | -0.25                   |
| latte                | 2.75         | 2.75         | **Missing**   | N/A                     | N/A                     |
| latte macchiato      | 3.00         | 3.00         | 3.00          | ✓ Match                 | ✓ Match                 |
| flat white           | 3.00         | 3.00         | 3.00          | ✓ Match                 | ✓ Match                 |
| oat/soy/coconut milk | 0.25         | 0.25         | 0.30          | +0.05                   | +0.05                   |
| tea                  | 2.50         | 2.50         | 2.50          | ✓ Match                 | ✓ Match                 |
| ginger tea           | 2.75         | 2.75         | 2.90          | +0.15                   | +0.15                   |
| mint tea             | 2.75         | 2.75         | 2.90          | +0.15                   | +0.15                   |
| chai latte           | 3.00         | 3.00         | 3.00          | ✓ Match                 | ✓ Match                 |

**Key Issues:**

- **Missing items**: `cortado` and `latte` are in OCR results but missing from `08.json`
- **Price discrepancies**: Several items have different prices between OCR and final JSON
- **Inconsistent recognition**: OCR found 27 items, but `08.json` has fewer articles

This confirms your feedback about recognition accuracy issues and the need for easy correction tools.
