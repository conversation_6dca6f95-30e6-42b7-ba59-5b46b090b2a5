=== STDOUT ===
```json
{
  "restaurant": {
    "name": "",
    "phone": "",
    "address": ""
  },
  "menu": {
    "groups": [
      {
        "name": "Non Alcohol",
        "vat_rate": "Reduced",
        "departments": [
          {
            "name": "Hot Drinks",
            "articles": [
              {
                "name": "Espresso",
                "description": "",
                "price": 2.50,
                "modifiers": []
              },
              {
                "name": "Tea",
                "description": "",
                "price": 3.00,
                "modifiers": []
              }
            ]
          },
          {
            "name": "Cold Drinks",
            "articles": [
              {
                "name": "Orange Juice",
                "description": "",
                "price": 4.00,
                "modifiers": []
              },
              {
                "name": "Sparkling Water",
                "description": "",
                "price": 3.00,
                "modifiers": []
              }
            ]
          }
        ]
      }
    ]
  },
  "confidence": {
    "articles": 92,
    "articles_problem": "poor lighting",
    "departments": 94,
    "departments_problem": "unclear text",
    "groups": 90,
    "groups_problem": "blurred",
    "vat_rates": 100,
    "vat_rates_problem": "",
    "restaurant_name": 85,
    "restaurant_name_problem": "handwritten",
    "restaurant_phone": 85,
    "restaurant_phone_problem": "handwritten",
    "restaurant_address": 85,
    "restaurant_address_problem": "handwritten"
  }
}
```

=== STDERR ===
Analyzing image .\12.jpeg...
Model: gpt-4o, Detail: low, Crop: 1024
Resizing image to max 1024px...
Encoding image...
Generating payload...
Sending request to api.openai.com...
Time spent: 14.65 seconds

=== RETURN CODE ===
0