import os
from PIL import Image

def get_image_info():
    current_dir = os.getcwd()
    image_extensions = ('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp')
    
    for filename in os.listdir(current_dir):
        if filename.lower().endswith(image_extensions):
            try:
                with Image.open(filename) as img:
                    width, height = img.size
                    file_size = os.path.getsize(filename)
                    file_size_kb = file_size / 1024
                    
                    print(f"{filename}: {width}x{height} pixels, {file_size_kb:.1f} KB")
            except Exception as e:
                print(f"{filename}: Error - {e}")

if __name__ == "__main__":
    get_image_info()