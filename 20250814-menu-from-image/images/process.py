"""
Processes images specified by parameters

Parameters:

- If parameters are specified then they are considered as image names
- Else all image names in the current folder will be processed


Behavior:

- If {image_basename}.json exists then the image is skipped
- For all images process-one.py is executed
- If it succeeds then {image_basename}.json is created with the image metadata
- In any case {image_basename}_stdouterr.txt is created with the stdout/stderr output of the process-one.py
-
"""

import sys
import os
import subprocess
import glob
import json
from pathlib import Path


def get_image_files(directory="."):
    """Get all image files in the directory"""
    image_extensions = [
        "*.png",
        "*.jpg",
        "*.jpeg",
        "*.gif",
        "*.bmp",
        "*.tiff",
        "*.webp",
    ]
    image_files = set()  # Use set to avoid duplicates

    for extension in image_extensions:
        image_files.update(glob.glob(os.path.join(directory, extension)))
        image_files.update(glob.glob(os.path.join(directory, extension.upper())))

    return sorted(list(image_files))


def process_image(image_path, prompt_path="prompt.md"):
    """Process a single image using process-one.py"""
    image_basename = Path(image_path).stem
    stdout_stderr_file = f"{image_basename}_stdouterr.txt"
    json_output_file = f"{image_basename}.json"

    # Skip if JSON file already exists
    if os.path.exists(json_output_file):
        print(f"⏭️  Skipping {image_path} (JSON already exists: {json_output_file})")
        return True

    print(f"Processing {image_path}...")

    try:
        # Run process-one.py and capture both stdout and stderr
        result = subprocess.run(
            [sys.executable, "process-one.py", image_path, prompt_path],
            capture_output=True,
            text=True,
            timeout=300,  # 5 minute timeout
        )

        # Write stdout and stderr to file
        with open(stdout_stderr_file, "w", encoding="utf-8") as f:
            f.write("=== STDOUT ===\n")
            f.write(result.stdout)
            f.write("\n=== STDERR ===\n")
            f.write(result.stderr)
            f.write(f"\n=== RETURN CODE ===\n")
            f.write(str(result.returncode))

        # If process succeeded, try to parse and save JSON output
        if result.returncode == 0 and result.stdout.strip():
            try:
                # Clean the output - remove markdown code fences if present
                output = result.stdout.strip()

                # Remove ```json at the beginning and ``` at the end
                if output.startswith("```json"):
                    output = output[7:]  # Remove ```json
                elif output.startswith("```"):
                    output = output[3:]  # Remove ```

                if output.endswith("```"):
                    output = output[:-3]  # Remove trailing ```

                output = output.strip()

                # Try to parse the cleaned output as JSON
                json_data = json.loads(output)

                # Write JSON to file
                with open(json_output_file, "w", encoding="utf-8") as f:
                    json.dump(json_data, f, indent=2, ensure_ascii=False)

                print(f"✓ Successfully processed {image_path} -> {json_output_file}")
                return True

            except json.JSONDecodeError as e:
                print(f"✗ Failed to parse JSON output for {image_path}: {e}")
                print(f"  Raw output saved to {stdout_stderr_file}")
                return False
        else:
            print(
                f"✗ Process failed for {image_path} (return code: {result.returncode})"
            )
            print(f"  Output saved to {stdout_stderr_file}")
            return False

    except subprocess.TimeoutExpired:
        print(f"✗ Timeout processing {image_path}")
        with open(stdout_stderr_file, "w", encoding="utf-8") as f:
            f.write("=== TIMEOUT ===\n")
            f.write("Process timed out after 5 minutes")
        return False

    except Exception as e:
        print(f"✗ Error processing {image_path}: {e}")
        with open(stdout_stderr_file, "w", encoding="utf-8") as f:
            f.write("=== ERROR ===\n")
            f.write(str(e))
        return False


def main():
    """Main function to process images"""
    # Check if prompt.md exists
    prompt_path = "prompt.md"
    if not os.path.exists(prompt_path):
        print(f"Error: Prompt file '{prompt_path}' not found.")
        sys.exit(1)

    # Determine which images to process
    if len(sys.argv) > 1:
        # Use command line arguments as image names
        image_files = sys.argv[1:]

        # Validate that all specified files exist
        for image_file in image_files:
            if not os.path.exists(image_file):
                print(f"Error: Image file '{image_file}' not found.")
                sys.exit(1)
    else:
        # Get all image files in current directory
        image_files = get_image_files()

        if not image_files:
            print("No image files found in current directory.")
            sys.exit(0)

    print(f"Found {len(image_files)} image(s) to process:")
    for img in image_files:
        print(f"  - {img}")
    print()

    # Process each image
    successful = 0
    failed = 0

    for image_file in image_files:
        if process_image(image_file, prompt_path):
            successful += 1
        else:
            failed += 1

    print(f"\nProcessing complete:")
    print(f"  ✓ Successful: {successful}")
    print(f"  ✗ Failed: {failed}")
    print(f"  Total: {len(image_files)}")

    if failed > 0:
        sys.exit(1)


if __name__ == "__main__":
    main()
