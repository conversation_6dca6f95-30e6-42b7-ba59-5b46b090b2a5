# AIR-1348

- https://untill.atlassian.net/browse/AIR-1348

## Motivation

**UX Specialists**, **Business Team**, **Marketing Team**, and the **Product Owner** need to assess whether AI can reliably recognize **Articles**, **Departments**, **Groups**, and **VAT rates** from menu images.

This research will determine the feasibility of:

- Automating menu setup during onboarding
- Reducing manual input
- Improving the initial customer experience

Since VAT compliance is mandatory in the Netherlands, correct tax group recognition will be a critical success factor. The results of this research will directly inform and guide **future UX design** for onboarding workflows in unTill Air.

## User Story

As a **UX Specialist, Business Team Member, Marketing Team Member, or Product Owner**, I want to research whether AI can accurately extract Articles, Departments, Groups, and VAT rates from menu pictures, so that I can decide how this technology can be used in onboarding and prepare UX designs that integrate it effectively.

## Acceptance Criteria

✅ Collect and provide **real-world menu examples** from the Netherlands (attached to task).

✅ Run AI extraction tests to detect:

- Articles (names and prices)
- Departments (e.g., Drinks, Starters, Main Dishes)
- Groups (logical item grouping as used in Back Office)
- VAT rates based on Dutch regulations
  - 9% (reduced rate): all food and non-alcoholic drinks (coffee, tea, soft drinks, juices), whether eaten in or taken away.
  - 21% (standard rate): alcoholic drinks (beer >0.5% ABV; other drinks >1.2% ABV), plus most non-food retail (e.g., mugs, merch). Cocktails, wine, spirits, regular beer all sit here.

✅ Evaluate extraction accuracy:

- Target: 90%+ correct recognition for articles and VAT

✅ Document findings with:

- Average cost for image processing
- Accuracy percentages per category (e.g., recognized correctly: 98% of articles, 95% of departments, 96% of groups, 91% of VAT rates)
- Common error patterns
- Recommendations for UX integration

## Functional Design – Research Scenarios

### High-quality printed menu

- AI processes a clear, Dutch-language menu photo
- Correctly extracts all articles, departments, groups, and assigns VAT

### Low-quality image

- AI processes a blurry or poorly lit photo, flags low-confidence extractions for manual check

### Mixed language menu

- AI detects and processes menus with Dutch and English text, applies Dutch VAT rules

### VAT ambiguity

- AI cannot assign VAT due to missing clues; such items are marked for manual assignment

### Multiple price options

- AI detects different sizes/variants under the same article and structures them accordingly

## Images

- [images](images/)
