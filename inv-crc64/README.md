### Overview

Due to Javascript core restrictions the Number type values are in range  from -(2^53-1) to (2^53-1) and can't contain int64 values. 

So I used a `crc32` hash function

### Testing 

1. Write some strings to `data.json` file as an array of strings.
2. run `go run main.go` - `hashes.json` will be generated
3. run `npm i`
3. run `node index.js` - if all hashes are equal, you should see `All hashes are equal` in console


### Notes

- I used this npm module for crc32 - https://www.npmjs.com/package/fast-crc32c