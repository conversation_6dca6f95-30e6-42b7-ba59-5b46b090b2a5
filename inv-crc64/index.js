const crc64 = require('crc64-ecma182.js');
const crc32 = require('fast-crc32c');

let Hashes = require('./hashes.json');
let Data = require('./data.json');

for (let i = 0; i < Data.length; i++) {
    const t = crc32.calculate(Data[i]);

    if (Hashes[i] != t) {
        throw new Error(`hashes are nor equal for input data ${i}:${Data[i]} ( ${Hashes[i]} != ${t})`);
    }
}

console.log("All hashes are equal");