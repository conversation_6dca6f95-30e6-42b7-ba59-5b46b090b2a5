package main

import (
	"encoding/json"
	"hash/crc32"
	"io/ioutil"
)

const inputFile = "./data.json"
const outputFile = "./hashes.json"

func check(e error) {
	if e != nil {
		panic(e)
	}
}

func main() {
	content, _ := ioutil.ReadFile(inputFile) // filename is the JSON file to read

	var data interface{}

	err := json.Unmarshal(content, &data)

	check(err)

	t := crc32.MakeTable(crc32.Castagnoli)

	inputData := data.([]interface{})

	res := make([]uint32, len(inputData))

	for i, s := range inputData {
		res[i] = crc32.Checksum([]byte(s.(string)), t)
	}

	content, err = json.MarshalIndent(res, "", " ")

	check(err)

	err = ioutil.WriteFile(outputFile, content, 0644)
	check(err)
}
