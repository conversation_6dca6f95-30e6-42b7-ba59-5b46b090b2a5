time spent: 9.66 seconds
Here's the menu structure and restaurant information in JSON format:

### Menu Structure in JSON

```json
{
  "Categories": [
    {
      "Category": "Main Course",
      "Groups": [
        {
          "Group": "Main Dishes",
          "Departments": [
            {
              "Department": "Burgers & Sandwiches",
              "Articles": [
                {"Name": "Cheeseburger", "Price": 34},
                {"Name": "Cheese sandwich", "Price": 22},
                {"Name": "Chicken burgers", "Price": 23},
                {"Name": "Spicy chicken", "Price": 33},
                {"Name": "Hot dog", "Price": 24}
              ]
            }
          ]
        }
      ]
    },
    {
      "Category": "Appetizers",
      "Groups": [
        {
          "Group": "Starters",
          "Departments": [
            {
              "Department": "Small Bites",
              "Articles": [
                {"Name": "Fruit Salad", "Price": 13},
                {"Name": "Cocktails", "Price": 12},
                {"Name": "Nuggets", "Price": 14},
                {"Name": "Sandwich", "Price": 13},
                {"Name": "French Fries", "Price": 15}
              ]
            }
          ]
        }
      ]
    },
    {
      "Category": "Beverages",
      "Groups": [
        {
          "Group": "Drinks",
          "Departments": [
            {
              "Department": "Non-Alcoholic Drinks",
              "Articles": [
                {"Name": "Milk Shake", "Price": 3},
                {"Name": "Iced Tea", "Price": 2},
                {"Name": "Orange Juice", "Price": 4},
                {"Name": "Lemon Tea", "Price": 3},
                {"Name": "Coffee", "Price": 5}
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

### Restaurant Information in JSON

```json
{
  "Restaurant": {
    "Name": "Paucek and Lage Restaurant",
    "Phone": "************",
    "Address": "123 Anywhere St., Any City"
  }
}
```
