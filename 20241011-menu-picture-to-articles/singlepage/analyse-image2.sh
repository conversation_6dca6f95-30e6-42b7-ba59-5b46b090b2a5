#!/bin/bash
set -Eeuo pipefail

# OpenAI API Key
api_key=$OPENAI_API_KEY 

# Path to your image
image_path=$1

# Encode image to base64
base64_image=$(base64 -w 0 "$image_path")

echo OK!

# Build the payload
payload=$(cat <<EOF
{
  "model": "gpt-4o-mini",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Whats in this image?"
        }
      ]
    }
  ],
  "max_tokens": 300
}
EOF
)


echo "$payload" > payload.out

# Write the payload to a temporary file
payload_file=$(mktemp)
echo "$payload" > "$payload_file"

# Send the POST request using the payload file
response=$(curl -s -X POST https://api.openai.com/v1/chat/completions \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $api_key" \
    --data @"$payload_file")

# Clean up the temporary file
rm "$payload_file"

# Print the response
echo "$response"