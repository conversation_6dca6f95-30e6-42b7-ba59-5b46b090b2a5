import sys
import os
import json
import base64
import requests
import time

# OpenAI API Key
api_key = os.getenv("OPENAI_API_KEY")

# Function to encode the image
def encode_image(image_path):
  with open(image_path, "rb") as image_file:
    return base64.b64encode(image_file.read()).decode('utf-8')

# Check if at least one argument is passed
if len(sys.argv) < 3:
    print("Use: python menu.py <image-path> <prompt-path>")
    sys.exit(1)

# Access the first argument
image_path = sys.argv[1]

# Access the second argument
prompt_path = sys.argv[2]

# Check if the filename ends with .jpg extension
if not image_path.lower().endswith('.jpg'):
    print("Error: The provided file does not have a .jpg extension.")
    sys.exit(1)

print(f"analyze image {image_path}...", file=sys.stderr)

print("encode image...", file=sys.stderr)
# Getting the base64 string
base64_image = encode_image(image_path)

headers = {
  "Content-Type": "application/json",
  "Authorization": f"Bearer {api_key}"
}

print("generate payload ...", file=sys.stderr)
# Open the file in read mode and read its contents
with open(prompt_path, 'r') as file:
    prompt = file.read()

payload = {
  "model": "gpt-4o",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": f"{prompt}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": f"data:image/jpeg;base64,{base64_image}"
          }
        }
      ]
    }
  ],
  "max_tokens": 2000
}


# Record the start time
response_start_time = time.time()

print("send the request api.openai.com ...", file=sys.stderr)
response = requests.post("https://api.openai.com/v1/chat/completions", headers=headers, json=payload)

# Calculate and print the time spent
time_spent = time.time() - response_start_time

print(f"time spent: {time_spent:.2f} seconds", file=sys.stderr)
print(f"time spent: {time_spent:.2f} seconds")


response_json = response.json()

content = response_json["choices"][0]["message"]["content"]

print(content)