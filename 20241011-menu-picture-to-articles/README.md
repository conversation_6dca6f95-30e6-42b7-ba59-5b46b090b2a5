# Menu picture to articles

## Motivation

- [dev.untill.com: Menu picture to articles](https://dev.untill.com/projects/#!711853)

This research explores how to extract menu information from images, using AI tools to automate the process. The goal is to compare various methods and estimate the feasibility of implementing similar functionality. For comparison, we will try to guess how <PERSON><PERSON><PERSON> handles similar tasks.

## Objectives

- Learn the ability to recognize and extract menu and other relevant information from images.
- Investigate how <PERSON><PERSON><PERSON> achieves this and compare the results.
- Estimate the time and resources required to implement this functionality.

## Method

- Use ChatGPT and Midjorney UI
- Use OpenAI  API
- Compare results with Butlaroo

## Context

### openai.com

Config:

- https://platform.openai.com/settings/organization/api-keys
- https://platform.openai.com/settings/organization/usage
- https://platform.openai.com/settings/profile

API:

- https://platform.openai.com/docs/guides/vision
- https://platform.openai.com/docs/api-reference/images/create

### Bootlaroo

- [Butlaroo Onboarding](https://dashboard.butlaroo.com/onboarding)
  - [Location1, burgers](https://dashboard.butlaroo.com/onboarding?token=eyJraWQiOiJhZjFlYzlkNC1hZjJkLTQ3N2MtYWNkYS01NDIxZTk2ZGRhMmYiLCJhbGciOiJFUzI1NiJ9&token=*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************&token=tvif-rrxa8GOmrwNr41U14QFRR4aNRMe0oX2kerM0R3pgcF-Rh_AtTMQ1YGmkZO8OCuDEWx084ty7hkSsRyV6w)
  - [Location2, steaks](https://dashboard.butlaroo.com/onboarding?token=eyJraWQiOiJhZjFlYzlkNC1hZjJkLTQ3N2MtYWNkYS01NDIxZTk2ZGRhMmYiLCJhbGciOiJFUzI1NiJ9&token=***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************&token=P8DqKjg9WZwMFtp_6a-2uw9RAw6kzdDmKvUfZ0AluuEKgwwXPaeoepNo7wF4Jt6_5JY9F8r3j3aNJfwmzCWS-A)
  - [Location3, drinks](https://dashboard.butlaroo.com/onboarding?token=eyJraWQiOiJhZjFlYzlkNC1hZjJkLTQ3N2MtYWNkYS01NDIxZTk2ZGRhMmYiLCJhbGciOiJFUzI1NiJ9&token=******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************&token=q4Fl9YDx32QW5cEi4BRUyuAC4es7kyNl8Gr9iTRSIVxCU4WQdWD6oZbAiY1zEnJxv8jFo86HqwYVYuhmHIRNtg)

## Results from ChatGPT UI, menu structure

A menu JSON structure was generated using ChatGPT for several restaurant categories, such as appetizers and main courses. This was not shared publicly due to access limitations but involved generating structured data from the image content.

- ChatGPT: [Restaurant Menu JSON Structure](https://chatgpt.com/c/6703ba8e-6418-800d-be0d-61403e979b2b) (sorry, it is not shared)

## Results from ChatGPT UI, styled appetizers

- ChatGPT: [Stylized apppetizer images](https://chatgpt.com/g/g-2fkFE8rbu-dall-e/c/6703f29a-e984-800d-8e5d-1c79ea87f78f) (sorry, it is not shared)

---
![alt text](image-1.png)

---

![alt text](image-2.png)

---

![alt text](image-3.png)

## Results from Midjorney UI, styled appetizers

- Midjourney: [Appetizer Images](https://discord.com/channels/1170352149230788678/1292843466253926501) (sorry, it is not shared)

![alt text](image-4.png)

---

![alt text](image-5.png)

## Results from OpenAI  API, menu structure, single page

---
Extract menu from image, "dark menu".

[singlepage/menu-dark-menu.sh](singlepage/menu-dark-menu.sh)

![alt text](singlepage/menu-dark.jpg)

```text
time spent: 7.18 seconds
Here's the restaurant menu structured as JSON:
```

```json
{
  "Department": "Paucek and Lage Restaurant",
  "Groups": [
    {
      "Group": "Main Course",
      "Categories": [
        {
          "Category": "Main Course Items",
          "Articles": [
            {"name": "Cheeseburger", "price": 34},
            {"name": "Cheese sandwich", "price": 22},
            {"name": "Chicken burgers", "price": 23},
            {"name": "Spicy chicken", "price": 33},
            {"name": "Hot dog", "price": 24}
          ]
        }
      ]
    },
    {
      "Group": "Appetizers",
      "Categories": [
        {
          "Category": "Appetizer Items",
          "Articles": [
            {"name": "Fruit Salad", "price": 13},
            {"name": "Cocktails", "price": 12},
            {"name": "Nuggets", "price": 14},
            {"name": "Sandwich", "price": 13},
            {"name": "French Fries", "price": 15}
          ]
        }
      ]
    },
    {
      "Group": "Beverages",
      "Categories": [
        {
          "Category": "Beverage Items",
          "Articles": [
            {"name": "Milk Shake", "price": 3},
            {"name": "Iced Tea", "price": 2},
            {"name": "Orange Juice", "price": 4},
            {"name": "Lemon Tea", "price": 3},
            {"name": "Coffee", "price": 5}
          ]
        }
      ]
    }
  ]
}
```

---
Extract company information from image, "dark menu".

[singlepage/menu-dark-company.sh](singlepage/menu-dark-company.sh)

```json
{
  "name": "Pauceck and Lage Restaurant",
  "phone": "************",
  "address": "123 Anywhere St., Any City"
}
```

---

Extract menu and company information from image, "dark menu".

[singlepage/menu-dark-all.sh](singlepage/menu-dark-all.sh)

```json
{
  "Categories": [
    {
      "Category": "Main Course",
      "Groups": [
        {
          "Group": "Main Dishes",
          "Departments": [
            {
              "Department": "Burgers & Sandwiches",
              "Articles": [
                {"Name": "Cheeseburger", "Price": 34},
                {"Name": "Cheese sandwich", "Price": 22},
                {"Name": "Chicken burgers", "Price": 23},
                {"Name": "Spicy chicken", "Price": 33},
                {"Name": "Hot dog", "Price": 24}
              ]
            }
          ]
        }
      ]
    },
    {
      "Category": "Appetizers",
      "Groups": [
        {
          "Group": "Starters",
          "Departments": [
            {
              "Department": "Small Bites",
              "Articles": [
                {"Name": "Fruit Salad", "Price": 13},
                {"Name": "Cocktails", "Price": 12},
                {"Name": "Nuggets", "Price": 14},
                {"Name": "Sandwich", "Price": 13},
                {"Name": "French Fries", "Price": 15}
              ]
            }
          ]
        }
      ]
    },
    {
      "Category": "Beverages",
      "Groups": [
        {
          "Group": "Drinks",
          "Departments": [
            {
              "Department": "Non-Alcoholic Drinks",
              "Articles": [
                {"Name": "Milk Shake", "Price": 3},
                {"Name": "Iced Tea", "Price": 2},
                {"Name": "Orange Juice", "Price": 4},
                {"Name": "Lemon Tea", "Price": 3},
                {"Name": "Coffee", "Price": 5}
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

```json
{
  "Restaurant": {
    "Name": "Paucek and Lage Restaurant",
    "Phone": "************",
    "Address": "123 Anywhere St., Any City"
  }
}
```

## Results from OpenAI  API, multiple pages

The following document was processed: [Vlees & Co (Eng).pdf](<multipage/Vlees & Co (Eng).pdf>).

Processing this document in UI failed, so I splitted the document into pages and used per single page.

---
The most complex page is page #2:

[multipage/page2.sh](multipage/page2.sh)

![alt text](multipage/page-2.jpg)

Result, time spent: 19.62 seconds
```json
{
  "Menu": {
    "Categories": [
      {
        "Name": "Signature Steaks",
        "Groups": [
          {
            "Name": "Grass Fed",
            "Departments": [
              {
                "Name": "Steaks",
                "Articles": [
                  {
                    "Name": "Picanha | Uruguay Angus | Grain Fed 250 Grams",
                    "Description": "Also known as the rump cap, this tender steak gets its delicious flavor from the beautiful fat cap.",
                    "Price": 27.50,
                    "VAT Group": "Food and drink (non-alcoholic)"
                  },
                  {
                    "Name": "Tenderloin | Nature’s Reserve | Grain Fed 200 Grams",
                    "Description": "The tenderloin guarantees ultimate tenderness. The grain nutrition gives this steak a rich and robust taste.",
                    "Price": 34.50,
                    "VAT Group": "Food and drink (non-alcoholic)"
                  },
                  {
                    "Name": "Rib Eye | Uruguay Angus | Grain Fed 250 Grams",
                    "Description": "The classic rib eye gets its flavor partly due to the ‘fat eye’ in the middle of the steak and has a delicious bite.",
                    "Price": 30.00,
                    "Modifier": [
                      {
                        "Name": "350 Grams",
                        "Price": 39.00,
                        "VAT Group": "Food and drink (non-alcoholic)"
                      }
                    ],
                    "VAT Group": "Food and drink (non-alcoholic)"
                  }
                ]
              }
            ]
          },
          {
            "Name": "Dry Aged",
            "Departments": [
              {
                "Name": "Steaks",
                "Articles": [
                  {
                    "Name": "Rib Eye | Dry Aged | Weiderund, NL 250 Grams",
                    "Description": "This grass fed Ribeye is specially selected by our butcher for its beautiful marbling.",
                    "Price": 32.50,
                    "VAT Group": "Food and drink (non-alcoholic)"
                  },
                  {
                    "Name": "Côte de Boeuf | Dry Aged | Weiderund, NL per 100 Grams",
                    "Description": "The climate and vast stretches of meadow on which the cattle graze ensures the characteristics of the taste and texture. Starting at 600 grams, ask for the availability.",
                    "Price": 13.00,
                    "VAT Group": "Food and drink (non-alcoholic)"
                  }
                ]
              }
            ]
          },
          {
            "Name": "Creekstone Farms USA | Premium Grain Fed",
            "Departments": [
              {
                "Name": "Steaks",
                "Articles": [
                  {
                    "Name": "Bavette | Creekstone Black Angus 200 Grams",
                    "Description": "Creekstone Black Angus is known worldwide for its fantastic quality. This Bavette is buttery tender, with high marbling and a nice bite.",
                    "Price": 29.50,
                    "VAT Group": "Food and drink (non-alcoholic)"
                  },
                  {
                    "Name": "Rib Steak | Creekstone Black Angus 250 Grams",
                    "Description": "The Rib Steak is cut from the neck and has a high marbling and nice fatty texture, which gives the steak a beautiful succulent and rich taste.",
                    "Price": 31.00,
                    "VAT Group": "Food and drink (non-alcoholic)"
                  }
                ]
              }
            ]
          },
          {
            "Name": "World Steak Challenge Winners",
            "Departments": [
              {
                "Name": "Steaks",
                "Articles": [
                  {
                    "Name": "Rib Eye | Creekstone Black Angus | USA 250 Grams",
                    "Description": "Best steak of North-America. This Rib Eye has a beautiful marbling, amazing tenderness and a rich meaty flavor.",
                    "Price": 49.50,
                    "VAT Group": "Food and drink (non-alcoholic)"
                  },
                  {
                    "Name": "Entrecôte | J-Wagyu A5 | Japan BMS 9+ | 100 or 200 Gram",
                    "Description": "Best Wagyu Steak. This cattle live in a beautiful environment. With the highest Wagyu grade and Best BMS, this is the ultimate taste sensation in the world of Wagyu beef.",
                    "Price": 56.50,
                    "Modifier": [
                      {
                        "Name": "200 Gram",
                        "Price": 113.00,
                        "VAT Group": "Food and drink (non-alcoholic)"
                      }
                    ],
                    "VAT Group": "Food and drink (non-alcoholic)"
                  },
                  {
                    "Name": "Entrecôte | Jack’s Creek F1 Wagyu | Australïe BMS 8/9 | 200 Gram",
                    "Description": "World’s Best Steak This cross breed of Wagyu and Black Angus from the Australian Jack's Creek farm has won multiple awards in the past years. This year they have won the following awards: World’s Best Steak, World’s Best Sirloin, World’s Best Grain Fed Steak, Oceania’s Best Steak. With these titles you are guaranteed an amazing steak!",
                    "Price": 80.00,
                    "VAT Group": "Food and drink (non-alcoholic)"
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        "Name": "Side Dishes",
        "Groups": [],
        "Departments": [
          {
            "Name": "Sides",
            "Articles": [
              {
                "Name": "Extra Portion of Fries",
                "Price": 4.00,
                "VAT Group": "Food and drink (non-alcoholic)"
              },
              {
                "Name": "Truffle Parmesan Fries",
                "Price": 7.00,
                "VAT Group": "Food and drink (non-alcoholic)"
              },
              {
                "Name": "Green Salad",
                "Price": 5.00,
                "VAT Group": "Food and drink (non-alcoholic)"
              },
              {
                "Name": "Jacket Potato | Tzatziki | Cheese",
                "Price": 5.00,
                "VAT Group": "Food and drink (non-alcoholic)"
              },
              {
                "Name": "Beetroot | Almonds | Crème Fraîche",
                "Price": 5.00,
                "VAT Group": "Food and drink (non-alcoholic)"
              },
              {
                "Name": "Baby Spinach Salad | Feta | Walnut",
                "Price": 5.00,
                "VAT Group": "Food and drink (non-alcoholic)"
              },
              {
                "Name": "Grilled Corn on the Cob",
                "Price": 5.00,
                "VAT Group": "Food and drink (non-alcoholic)"
              },
              {
                "Name": "Grilled Padrón Peppers",
                "Price": 5.00,
                "VAT Group": "Food and drink (non-alcoholic)"
              }
            ]
          }
        ]
      },
      {
        "Name": "Sauces",
        "Groups": [],
        "Departments": [
          {
            "Name": "Sauces",
            "Articles": [
              {
                "Name": "Sauces",
                "Description": "Choose one of our sauces or butters with your steak: Garlic veal gravy, Bearnaise, Spicy Chimichurri, Peppersauce, Garlicbutter",
                "Price": 2.40,
                "VAT Group": "Food and drink (non-alcoholic)"
              }
            ]
          }
        ]
      }
    ]
  }
}
```

## Results from OpenAI  API, drinks

[cocktails/drinks.sh](cocktails/drinks.sh)
![drinks](cocktails/drinks.jpg)

- time spent: ‼️33.62 seconds
- Here is a JSON representation of the menu structure based on the image:

```json
{
  "categories": [
    {
      "name": "Drinks",
      "groups": [
        {
          "name": "Signature Spirits",
          "departments": [
            {
              "name": "Cocktails",
              "articles": [
                {
                  "name": "Flaming Volcano",
                  "description": "An explosion of spirits and fruit juice served in a fiery volcano.",
                  "price": 18,
                  "vat_group": "Alcoholic"
                },
                {
                  "name": "Neo-Buddha",
                  "description": "Buddha’s visit to the neon night life of Tokyo. With the chemistry of Midori, Hypnotiq, Rum, and Pineapple Juice.",
                  "price": 10,
                  "vat_group": "Alcoholic"
                },
                {
                  "name": "Terra Cotta Zombie",
                  "description": "Unearth the blend of Curzan 151 & Absolut Citron and Pineapple Juice.",
                  "price": 10,
                  "vat_group": "Alcoholic"
                },
                {
                  "name": "Saketini",
                  "description": "Sake & Ketel One Vodka, Shaken until iced cold.",
                  "price": 9,
                  "vat_group": "Alcoholic"
                },
                {
                  "name": "Peking Mai Tai",
                  "description": "Delicious blend of Myers’s Original Dark Rum, Homemade Mai Tai Mix, Sour Mix, and Grenadine.",
                  "price": 9,
                  "vat_group": "Alcoholic"
                },
                {
                  "name": "Royal Jade",
                  "description": "Grey Goose, Cointreau, Midori, Hypnotiq, and a splash of lemon-lime.",
                  "price": 9,
                  "vat_group": "Alcoholic"
                },
                {
                  "name": "Tsingtao Fizz",
                  "description": "Beer Cocktail made of Tsingtao Ginger Ale and a splash of Peach Schnapps.",
                  "price": 8,
                  "vat_group": "Alcoholic"
                },
                {
                  "name": "Singapore Sling",
                  "description": "Sapphire Gin, Bénédictine D.O.M, Bitters, Cherry Brandy, Grenadine, Lemon Lime, and a Splash of Pineapple Juice.",
                  "price": 10,
                  "vat_group": "Alcoholic"
                },
                {
                  "name": "Han Purple",
                  "description": "Aperol, Absolut Citron, Club Soda, Welch’s Grape Juice.",
                  "price": 10,
                  "vat_group": "Alcoholic"
                },
                {
                  "name": "Indonesian Fire Dancer",
                  "description": "Curzan 151, Malibu Coconut Rum, Midori, Lemon-Lime, and Pineapple Juice.",
                  "price": 13,
                  "vat_group": "Alcoholic"
                }
              ]
            }
          ]
        },
        {
          "name": "Non-Alcoholic",
          "departments": [
            {
              "name": "Refillable",
              "articles": [
                {
                  "name": "Pepsi Products",
                  "price": 2.99,
                  "vat_group": "Food and drink (non-alcoholic)"
                },
                {
                  "name": "Hot Tea",
                  "price": 1.5,
                  "vat_group": "Food and drink (non-alcoholic)"
                },
                {
                  "name": "Fresh Brewed Iced Tea",
                  "price": 2.99,
                  "vat_group": "Food and drink (non-alcoholic)"
                }
              ]
            },
            {
              "name": "Non-Refillable",
              "articles": [
                {
                  "name": "Matcha Green Tea",
                  "price": 3,
                  "vat_group": "Food and drink (non-alcoholic)"
                },
                {
                  "name": "Milk",
                  "price": 3,
                  "vat_group": "Food and drink (non-alcoholic)"
                },
                {
                  "name": "Shirley Temple",
                  "price": 3,
                  "vat_group": "Food and drink (non-alcoholic)"
                },
                {
                  "name": "Apple Juice",
                  "price": 3,
                  "vat_group": "Food and drink (non-alcoholic)"
                },
                {
                  "name": "Orange Juice",
                  "price": 3,
                  "vat_group": "Food and drink (non-alcoholic)"
                },
                {
                  "name": "Pineapple Juice",
                  "price": 3,
                  "vat_group": "Food and drink (non-alcoholic)"
                },
                {
                  "name": "Cranberry Juice",
                  "price": 3,
                  "vat_group": "Food and drink (non-alcoholic)"
                }
              ]
            },
            {
              "name": "Boba Tapioca Tea",
              "articles": [
                {
                  "name": "Iced",
                  "price": 5,
                  "vat_group": "Food and drink (non-alcoholic)"
                },
                {
                  "name": "Milk Tea",
                  "description": "Options: Papaya, Banana, Strawberry, Taro, Honeydew, Matcha Green Tea",
                  "price": 5,
                  "vat_group": "Food and drink (non-alcoholic)"
                }
              ]
            }
          ]
        },
        {
          "name": "Beers",
          "departments": [
            {
              "name": "Bottles and Cans",
              "articles": [
                {
                  "name": "Free Verse (Virginia Beer Co.)",
                  "price": 5,
                  "vat_group": "Alcoholic"
                },
                {
                  "name": "Saving Daylight (Virginia Beer Co.)",
                  "price": 5,
                  "vat_group": "Alcoholic"
                },
                {
                  "name": "Tsing Tao",
                  "price": 4,
                  "vat_group": "Alcoholic"
                },
                {
                  "name": "Sapporo (22oz)",
                  "price": 6,
                  "vat_group": "Alcoholic"
                },
                {
                  "name": "Kirin Ichiban",
                  "price": 4,
                  "vat_group": "Alcoholic"
                },
                {
                  "name": "Stella Artois",
                  "price": 4,
                  "vat_group": "Alcoholic"
                },
                {
                  "name": "Corona",
                  "price": 4,
                  "vat_group": "Alcoholic"
                },
                {
                  "name": "Blue Moon",
                  "price": 4,
                  "vat_group": "Alcoholic"
                },
                {
                  "name": "Sam Adams",
                  "price": 4,
                  "vat_group": "Alcoholic"
                },
                {
                  "name": "Budweiser",
                  "price": 3,
                  "vat_group": "Alcoholic"
                },
                {
                  "name": "Bud Light",
                  "price": 3,
                  "vat_group": "Alcoholic"
                },
                {
                  "name": "Miller Lite",
                  "price": 3,
                  "vat_group": "Alcoholic"
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

---

## Findings

### Text-to-image

### Midjourney vs ChatGPT

Midjourney is better in generating styled images, but does not have official API.

### Prompt

A lot of depends on a prompt.

Prompt example:
```txt
// Ignore this line, it is a comment. Description, VAT group

I have the following restaurant menu hierarchy

- Article (say, Non-Alcoholic Mojito)
  - One or more Modifier (say, Ice)
- Department (say, Lemonades)
- Group (say, Non-Alcoholic)
- Category (say, Drinks)

Items structure:

- Article can have a description.
- Article and Modifier have optional price.
- Article and Modifier have VAT group, one of ["Food and drink (non-alcoholic)", "Alcoholic"].

Instructions:

- If Article contains multiple prices then the first one should be used as a price, the rest shall go to modifier list, try to guess the name of such modifiers.

Analyze the file, identify the menu structure, convert it to main and provide it in json format.
```

May be we need to include examples of departmens/groups/categories, ref. problem with "dark menu":

```json
{
  "Department": "Paucek and Lage Restaurant",
  "Groups": [
    {
      "Group": "Main Course",
      "Categories": [
        {
          "Category": "Main Course Items",
          "Articles": [
```

### Processing time

Depends on the image and prompt complexity. 5 (menu-green) - 33 (drinks) seconds.

### VAT group

It is possible to recognize VAT groups automatically.

```json
...
        {
            "name": "Han Purple",
            "description": "Aperol, Absolut Citron, Club Soda, Welch’s Grape Juice.",
            "price": 10,
            "vat_group": "Alcoholic"
        },
        {
            "name": "Indonesian Fire Dancer",
            "description": "Curzan 151, Malibu Coconut Rum, Midori, Lemon-Lime, and Pineapple Juice.",
            "price": 13,
            "vat_group": "Alcoholic"
        }
        ]
    }
    ]
},
{
    "name": "Non-Alcoholic",
    "departments": [
    {
        "name": "Refillable",
        "articles": [
        {
            "name": "Pepsi Products",
            "price": 2.99,
            "vat_group": "Food and drink (non-alcoholic)"
        },
        {
            "name": "Hot Tea",
            "price": 1.5,
            "vat_group": "Food and drink (non-alcoholic)"
        },
...        
```

### Validation

Results should be further validated (automatically) and previewed (by the end user).

### Which tool Butlaroo uses?

I think Butlaroo uses OpenAI  API, because:

- Processing time is similar.
- Results are similar.

Similar results, the original image (fragment):

![alt text](image-7.png)

Our results:
```json
      {
        "Name": "Sauces",
        "Groups": [],
        "Departments": [
          {
            "Name": "Sauces",
            "Articles": [
              {
                "Name": "Sauces",
                "Description": "Choose one of our sauces or butters with your steak: Garlic veal gravy, Bearnaise, Spicy Chimichurri, Peppersauce, Garlicbutter",
                "Price": 2.40,
                "VAT Group": "Food and drink (non-alcoholic)"
              }
            ]
          }
        ]
      }
```

Theirs:

![alt text](image-6.png)

## Implementation estimation

A possible implementation involves splitting the document into pages and processing each individually, followed by uploading and validating the results.

A rough draft:

```mermaid
sequenceDiagram
    participant user as User
    participant onboard as Onboardingpage
    participant server as Server
    participant openai as api.openai.com

    
    user ->> onboard: Document
    onboard ->> onboard: Split document into pages
    onboard ->> server: Upload pages
    server ->> openai: Process pages
    server -->> onboard: Return results
    onboard ->> onboard: Postprocess (validate) results
    onboard -->> user: Show results
    user ->> user: Edit results
    user ->> onboard: Confirm results

    onboard ->> server: Create location      
```

TBD:

- Additional menu examples
- Better prompts
- Functional and technical design
- Onboardingpage (frontend developer)
- Backend part (backend developer)
- Documentation

Estimation

- Frontend: 80-120 hours
- Backend: 40 hours
- Other: 160 hours
