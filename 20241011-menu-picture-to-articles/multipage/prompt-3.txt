// Ignore me, this is a comment. Description, VAT group

I have the following restaurant menu hierarchy

- Article (say, Non-Alcoholic Mojito)
  - One or more Modifier (say, Ice)
- Department (say, Lemonades)
- Group (say, Non-Alcoholic)
- Category (say, Drinks)

Items structure:

- Article can have a description.
- Article and Modifier have optional price.
- Article and Modifier have VAT group, one of ["Food and drink (non-alcoholic)", "Alcoholic"].

Instructions:
- If Article contains multiple prices then the first one should be used as a price, the rest shall go to modifier list, try to guess the name of such modifiers.

Analyze the file, identify the menu structure, convert it to main and provide it in a json format.