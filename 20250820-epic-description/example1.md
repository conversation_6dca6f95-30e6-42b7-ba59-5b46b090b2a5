# Epic: User Authentication System

- From here: https://chatgpt.com/c/68a59205-aaf8-8322-a7c2-0a7781e7f0b4
  - Shared link: https://chatgpt.com/share/68a5a56f-b068-800b-a5b7-7cd28b6b0c12

## Motivation

During a recent demo, our main sponsor tried accessing the system. He had forgotten his password but found there was no way to reset it himself. This caused embarrassment for the team, risked the sponsor’s trust, and highlighted a critical usability gap.

To avoid such situations, we must implement a proper authentication system with:

* Multi-step **sign-up** flow
* Secure **sign-in**
* **Password recovery** (self-service + admin reset)

This will improve user satisfaction, reduce support workload, and strengthen trust.

---

## Stories

### Story 1: User Sign Up

**As a** new user
**I want** to create an account by providing personal details and confirming my email
**So that** I can start using the system securely.

**Acceptance Criteria**:

* Step 1: User enters name & email, system validates input.
* Step 2: User selects business type, system saves it.
* Step 3: Confirmation email is sent.
* Account remains inactive until email is confirmed.
* After confirmation, account is marked as active and user can sign in.

**Flow**:

```mermaid
flowchart TD
    A[Enter Name & Email] --> C[Select Business Type]
    C --> D[Confirm Email]
    D --> CA[Create Account]

    CA --> G1[Create Retail Account]
    CA --> G2[Create Food Account]

    G1 --> H[Show Account Created]
    G2 --> H[Show 'Account Created']

```

**Flow (sequence diagram)**:

```mermaid
sequenceDiagram
    participant U as User
    participant FE as Frontend
    participant BE as Backend
    participant EM as Email Service

    U->>FE: Enter Name & Email
    FE->>BE: Send Name & Email
    BE-->>FE: Validate & Save (pending confirmation)

    U->>FE: Select Business Type
    FE->>BE: Save Business Type

    BE->>EM: Send confirmation email
    EM-->>U: Confirmation link

    U->>FE: Click confirmation link
    FE->>BE: Verify email
    BE-->>FE: Mark account as active
    FE-->>U: Show "Account Created"
```

---

### Story 2: User Sign In

**As a** registered user
**I want** to log in with my credentials
**So that** I can securely access my account.

**Acceptance Criteria**:

* Only verified accounts can log in.
* Valid credentials create session/token.
* Invalid credentials show error.
* Multiple failed attempts are rate-limited.

---

### Story 3: Recover Password (Self-service)

**As a** user who forgot my password
**I want** to reset it via email
**So that** I can regain access without waiting for support.

**Acceptance Criteria**:

* User enters email to request reset.
* If account exists, reset link is sent.
* Reset link expires after 1 hour.
* User sets new password and logs in successfully.

---

### Story 4: Reset Password by Admin

**As an** admin
**I want** to reset a user’s password
**So that** I can assist users who cannot reset it themselves.

**Acceptance Criteria**:

* Admin can search for a user and reset password.
* Temporary password or reset link generated.
* User notified by email.
* User must change password on next login.
* Reset action logged for audit.
