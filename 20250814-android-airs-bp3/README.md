# Android + AirsBP3 Server Evaluation

- **Author(s)**:
  - <PERSON>. unTill Software Development Group B. V.
  - <PERSON><PERSON>. unTill Software Development Group B. V.
  - <PERSON><PERSON> Nurmanov. unTill Software Development Group B. V.
- **Start date**: 2025-08-14
- **End date**: 2025-08-22
- **Keywords**: android, airsbp3, go mobile, performance testing, load testing

## Abstract

This evaluation assesses the feasibility and performance of running AirsBP3 server directly on Android devices using Go mobile bindings. The study addresses the need for edge computing capabilities in point-of-sale systems by implementing a complete AirsBP3 server stack on Android hardware.

The evaluation demonstrates successful implementation of a production-ready AirsBP3 server on Android with excellent performance characteristics. Using Go mobile bindings and Android foreground services, the system achieved 92.78-105.76 operations per second with sub-200ms average latencies under baseline conditions. Load testing revealed system scalability limits at 20x concurrent load (10 logins, 2 locations each), where latencies increased 15-20x but throughput remained stable. The implementation successfully handles real-world POS workloads with BBolt storage scaling from 256KB to 80MB under intensive load, proving Android devices can serve as reliable edge computing platforms for business-critical applications.

## Motivation

- **Edge Computing Requirements**: Modern POS systems need local processing capabilities to handle network outages and reduce latency
- **Hardware Cost Optimization**: Android devices provide cost-effective alternative to dedicated server hardware
- **Deployment Flexibility**: Mobile deployment enables rapid setup and configuration in diverse retail environments
- **Offline Capability**: Local server ensures business continuity during network disruptions

Proposed solution:

- Use Go mobile bindings to deploy AirsBP3 server directly on Android devices
  - Native performance through compiled Go code addresses processing requirements
  - Android foreground services provide persistent server operation
  - BBolt embedded database eliminates external dependencies

## Goals

- Evaluate the feasibility of running production AirsBP3 server on Android hardware
- Assess performance characteristics under various load conditions
- Validate system stability and reliability for business-critical operations
- Measure resource utilization and storage requirements

## Method

Overview:

- Implement AirsBP3 server in Go with mobile-compatible architecture
- Create Android application with server lifecycle management
- Deploy using Go mobile bindings and Android Archive (AAR) format
- Conduct comprehensive load testing using ABomber tool

Detailed plan:

- [Load Testing Results - Baseline Configuration](bombing-results/stats_1_login_1_location_per_login_2_mins/results.md)
- [Load Testing Results - Scaled Configuration](bombing-results/stats_10_logins_2_locations_per_login_2_mins/results.md)

## Background

- [Android Development Knowledge Base](kb.md)
- [AirEdge Implementation Details](airedge.md)

---

## Results

### Load Testing Evaluation

- Executed comprehensive load testing with ABomber tool
- Baseline test: [1 login, 1 location configuration](bombing-results/stats_1_login_1_location_per_login_2_mins/results.md)
- Scaled test: [10 logins, 2 locations configuration](bombing-results/stats_10_logins_2_locations_per_login_2_mins/results.md)

## Key Findings

### Performance Results

**Baseline Performance (1 login, 1 location):**

- Successfully achieved 92.78 operations per second over 2-minute test duration
- Perfect reliability with 100% success rate across 11,135 operations
- Excellent latency characteristics: 9ms average for orders, 12ms for bill processing
- Fast initialization: 164.63 ops/sec during environment setup (1.123 seconds)

**Scaled Performance (10 logins, 2 locations = 20x load):**

- Maintained throughput at 105.76 operations per second (+14% improvement)
- System stress indicators: 99.64% success rate with 46 failed operations (HTTP 503)
- Significant latency increase: 184ms average for orders (+1944%), 202ms for bills (+1583%)
- Initialization bottleneck: 91.46% success rate with 380 failed operations during setup

**Storage Efficiency:**

- BBolt database scaling: 256KB baseline to 64MB (1 location) and 80MB (20 locations)
- Efficient embedded storage with no external database dependencies
- Linear storage growth correlating with transaction volume

### Technical Insights

**Mobile Platform Capabilities:**

- Go mobile bindings provide near-native performance for server operations
- Android foreground services enable persistent server operation beyond app lifecycle
- Cross-platform compatibility verified across Android API levels 21+
- Successful Java-Go interoperability through JNI bindings

**System Behavior Under Load:**

- Excellent baseline performance demonstrates Android hardware capability
- Clear scalability limits at 20x concurrent load with latency degradation
- Service availability issues during peak initialization phases
- Throughput resilience maintained despite increased response times

**Architecture Strengths:**

- Dual operation modes (activity-based and foreground service) provide deployment flexibility
- Real-time status monitoring and lifecycle management
- Graceful shutdown and resource cleanup mechanisms
- Port-configurable deployment for network flexibility

### Limitations Identified

**Scalability Constraints:**

- 15-20x latency increase under 20x load indicates resource saturation
- Initialization phase shows higher failure rates (8.54% vs 0.36% during operations)
- Maximum latencies reach 819ms for orders under stress conditions

**Resource Management:**

- Background service CPU utilization during intensive operations
- Memory management considerations for sustained high-load scenarios
- Network timeout handling during peak concurrent access

### Recommendations

1. **For Production Deployment:** Android AirsBP3 server demonstrates excellent suitability for small to medium-scale POS operations with proper load management
2. **Hardware Requirements:** Modern Android devices (4+ cores, 4+ GB RAM, sufficient storage) adequate for typical retail scenarios
3. **Load Balancing:** Consider horizontal scaling with multiple Android devices for high-concurrency environments
4. **Monitoring:** Implement latency thresholds and alerting for production reliability
5. **Optimization:** Address initialization bottlenecks for improved startup performance under load

---

## Project Structure

```text
20250814-android-airsbp3/
├── airedge/                 # Android application
│   ├── app/src/main/java/   # Java source code
│   └── build.gradle.kts     # Android build configuration
├── go-airsbp3-server/       # AirsBP3 server implementation
│   ├── airsbp3server.go     # Core server logic
│   └── go.mod               # Go module definition
├── go-basic/                # Go mobile basic example
├── qimage/                  # Image generation tools
├── *.sh                     # Development automation scripts
└── README.md                # This documentation
```

## Quick Start

1. **Build Go AirsBP3 Server AAR**:
   ```bash
   cd go-airsbp3-server
   gomobile bind -androidapi 21 -target=android/arm64 -o ../airedge/app/libs/airsbp3server.aar
   ```

2. **Build Android Application**:
   ```bash
   cd airedge
   ./gradlew build
   ```

3. **Deploy to Device**:
   ```bash
   ./adb.sh install airedge/app/build/outputs/apk/debug/app-debug.apk
   ```

4. **Forward Ports for Testing**:
   ```bash
   ./adb-forward.sh
   ```

For detailed build instructions and troubleshooting, see `kb.md`.
