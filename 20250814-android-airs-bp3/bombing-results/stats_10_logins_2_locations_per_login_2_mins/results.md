# ABomber Load Testing Results - Scaled Configuration

## Test Configuration

### Environment Initialization
```bash
./abomber init --LoginPrefix=my__login --NumLogins=10 --NumLocationsPerLogin=2 --Password=123456 --ClusterURL=http://127.0.0.1:8822/ -v
```

### Load Test Execution
```bash
./abomber sales --LoginPrefix=my__login --NumLogins=10 --NumLocationsPerLogin=2 --Password=123456 --ClusterURL=http://127.0.0.1:8822/ -v --OrdersPerLocationPerDay=10 --Duration=2m -v
```

## Test Parameters
- **Remote cluster**: http://127.0.0.1:8822/
- **Cluster storage type**: bbolt
- **Login Prefix**: my__login
- **Number of Logins**: 10 (10x increase from previous test)
- **Locations per Login**: 2 (2x increase from previous test)
- **Total Locations**: 20 (20x increase from previous test)
- **Password**: 123456
- **Orders per Location per Day**: 10
- **Test Duration**: 2 minutes
- **Verbose Mode**: Enabled

## Overall Results

### Sales Test Summary
- **Total Duration**: 120.35 seconds (2 minutes)
- **Total Operations**: 12,729 (vs 11,135 in baseline test)
- **Successful Operations**: 12,729 (HTTP 200)
- **Failed Operations**: 46 (HTTP 503 - Service Unavailable)
- **Success Rate**: 99.64% (vs 100% in baseline test)
- **Operations per Second**: 105.76 ops/sec (vs 92.78 in baseline test)
- **Simulated Days**: 22 (vs 371 in baseline test)

### Initialization Summary
- **Duration**: 10.985 seconds (vs 1.123 seconds in baseline test)
- **Total Operations**: 4,071 (vs 185 in baseline test)
- **Successful Operations**: 4,071 (HTTP 200)
- **Failed Operations**: 380 (HTTP 503 - Service Unavailable)
- **Success Rate**: 91.46% (vs 100% in baseline test)
- **Operations per Second**: 370.57 ops/sec (vs 164.63 in baseline test)

## Detailed Performance Metrics

### c.air.Orders (Order Creation)
- **Total Operations**: 4,193
- **Success Rate**: 100% (HTTP 200)
- **Latency Statistics** (milliseconds):
  - **Minimum**: 6.71ms (vs 4.62ms baseline)
  - **Maximum**: 819.12ms (vs 52.35ms baseline)
  - **Average**: 184.07ms (vs 9.00ms baseline)
  - **50th Percentile**: 181.33ms (vs 8.42ms baseline)
  - **75th Percentile**: 222.76ms (vs 9.33ms baseline)
  - **90th Percentile**: 281.03ms (vs 10.92ms baseline)
  - **95th Percentile**: 320.53ms (vs 12.62ms baseline)
  - **99th Percentile**: 421.38ms (vs 20.60ms baseline)

### c.air.Pbill (Bill Processing)
- **Total Operations**: 4,193
- **Success Rate**: 100% (HTTP 200)
- **Latency Statistics** (milliseconds):
  - **Minimum**: 7.06ms (vs 6.21ms baseline)
  - **Maximum**: 747.86ms (vs 75.52ms baseline)
  - **Average**: 202.42ms (vs 12.01ms baseline)
  - **50th Percentile**: 200.42ms (vs 11.14ms baseline)
  - **75th Percentile**: 250.70ms (vs 12.63ms baseline)
  - **90th Percentile**: 304.39ms (vs 14.92ms baseline)
  - **95th Percentile**: 352.59ms (vs 16.89ms baseline)
  - **99th Percentile**: 467.29ms (vs 25.18ms baseline)

### c.registry.CreateLogin (Login Creation)
- **Total Operations**: 10 (one per login)
- **Success Rate**: 100% (HTTP 200)
- **Latency Statistics** (milliseconds):
  - **Minimum**: 97.31ms
  - **Maximum**: 172.48ms
  - **Average**: 145.82ms
  - **50th Percentile**: 149.44ms
  - **75th Percentile**: 154.51ms
  - **90th Percentile**: 170.87ms
  - **95th Percentile**: 172.48ms
  - **99th Percentile**: 172.48ms

### c.sys.InitChildWorkspace (Workspace Initialization)
- **Total Operations**: 20 (one per location)
- **Success Rate**: 100% (HTTP 200)
- **Latency Statistics** (milliseconds):
  - **Minimum**: 17.84ms
  - **Maximum**: 116.37ms
  - **Average**: 68.94ms
  - **50th Percentile**: 75.37ms
  - **75th Percentile**: 102.65ms
  - **90th Percentile**: 110.33ms
  - **95th Percentile**: 115.63ms
  - **99th Percentile**: 116.37ms

## Performance Analysis & Comparison

### Load Impact Assessment
1. **Throughput**: Despite 20x more locations, achieved 14% higher ops/sec (105.76 vs 92.78)
2. **Latency Degradation**: Significant increase in response times:
   - Order creation: 20x slower (184ms vs 9ms average)
   - Bill processing: 17x slower (202ms vs 12ms average)
3. **Error Introduction**: System started showing stress with 503 errors during initialization

### System Behavior Under Load
- **Resource Saturation**: Clear signs of system reaching capacity limits
- **Service Unavailability**: 380 failed operations during init, 46 during sales test
- **Latency Variance**: Much higher maximum latencies (819ms vs 52ms for orders)
- **Consistency Impact**: Higher percentile spreads indicating less predictable performance

### Critical Observations
1. **Scalability Limits**: 20x load increase revealed system bottlenecks
2. **Initialization Stress**: 8.5% failure rate during environment setup
3. **Runtime Stability**: Better performance during sales test (99.64% success)
4. **Throughput Resilience**: System maintained reasonable throughput despite stress

## Performance Comparison Summary

| Metric | Baseline Test (1 login, 1 location) | Scaled Test (10 logins, 2 locations) | Change |
|--------|-------------------------------------|---------------------------------------|---------|
| Total Locations | 1 | 20 | +1900% |
| Success Rate (Sales) | 100% | 99.64% | -0.36% |
| Success Rate (Init) | 100% | 91.46% | -8.54% |
| Ops/sec (Sales) | 92.78 | 105.76 | +14% |
| Ops/sec (Init) | 164.63 | 370.57 | +125% |
| Order Latency (Avg) | 9ms | 184ms | +1944% |
| Bill Latency (Avg) | 12ms | 202ms | +1583% |
| Max Order Latency | 52ms | 819ms | +1467% |
| Max Bill Latency | 75ms | 748ms | +897% |

## Conclusion

The scaled test (20x load increase) reveals important system characteristics:

### Strengths
- **Throughput Scaling**: System maintained and slightly improved ops/sec
- **Core Functionality**: Order and bill processing remained 100% successful
- **Recovery Capability**: Better performance during sales phase vs initialization

### Limitations Identified
- **Latency Degradation**: 15-20x increase in response times under load
- **Service Availability**: System shows stress with 503 errors during peak initialization
- **Scalability Ceiling**: Clear indication of approaching system capacity limits

### Recommendations
1. **Resource Scaling**: Consider horizontal or vertical scaling for higher loads
2. **Initialization Optimization**: Address bottlenecks in environment setup phase
3. **Load Balancing**: Implement strategies to handle concurrent workspace creation
4. **Performance Monitoring**: Establish latency thresholds and alerting for production use

The test successfully demonstrates both system capabilities and limitations under increased load.

### BBolt storage growth:
1. BBolt storage increased from 256KB to 80MB