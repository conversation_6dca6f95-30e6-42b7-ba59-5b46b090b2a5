package airsbp3server

var (
	secretsDir = "/data/data/com.untill.example.airedge/files/secrets"
)

var (
	secretKeyJWTFileName = "secretKeyJWT"
	secretKeyJWT         = "1234567812345678123456781234567812345678123456781234567812345678"

	fiscalCloudEnvCfgsFileName = "FiscalCloudEnvCfgs"
	fiscalConfig               = `{
    "emulation_mode": true,
    "management_api_url":"https://api.retailforce.cloud/api/v1.0",
    "fiscalization_api_url":"https://fiscalisation.retailforce.cloud/api/v1",
    "environments": {
        "test-air":{
            "subdistributor_id":"b7cb60cb-46a6-4dda-94ab-e5881e279202",
            "api_key":"RFC_7f0f6f57245746c0bec62f7b9995cbff",
            "api_secret":"huK-Y3bm1oMnAAJJtXREcBitjY-T89qXPQ",
            "test_terminals":true,
            "countries": {
                "DNK" : {
                    "configuration_id":"7215183b-9bb5-42f0-b156-be5b8462bc63",
                    "vatIDs": [
                        {"vatPercent": 25, "vatID": 1},
                        {"vatPercent": 0, "vatID": 2}
                    ]
                },
                "DEU": {
                    "configuration_id":"09c1a946-2168-4f36-a2c6-fef88d9389b2",
                    "vatIDs": [
                            {"vatPercent": 19, "vatID": 1},
                            {"vatPercent": 7, "vatID": 2},
                            {"vatPercent": 10.7, "vatID": 3},
                            {"vatPercent": 5.5, "vatID": 4},
                            {"vatPercent": 0, "vatID": 5}
                    ]
                }
            }
        }
    }
}`
)

const (
	storageTypeMemory = "memory"
	storageTypeBBolt  = "bbolt"
)
