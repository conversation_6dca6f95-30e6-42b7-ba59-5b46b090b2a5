package airsbp3server

import (
	"context"
	"fmt"
	"log"

	"github.com/voedger/voedger/pkg/pipeline"
)

type pipelineFunc func(_ context.Context, _ pipeline.IWorkpiece) error

type mockSecretsReader struct {
}

func (sr *mockSecretsReader) ReadSecret(name string) (bb []byte, err error) {
	switch name {
	case secretKeyJWTFileName:
		return []byte(secretKeyJWT), nil
	case fiscalCloudEnvCfgsFileName:
		return []byte(fiscalConfig), nil
	default:
		errUnknownSecret := fmt.Errorf("unknown secret name: %s", name)
		log.Printf(errUnknownSecret.Error())
		// Return nil to indicate that the secret is not found

		return nil, errUnknownSecret
	}
}
