module airsbp3server

go 1.24.4

toolchain go1.24.6

require (
	github.com/untillpro/airs-bp3 v1.202111031426.2-0.20250819115050-1bfad588766c
	github.com/voedger/voedger v0.0.0-20250819095258-cbef79893176
)

require (
	github.com/VictoriaMetrics/fastcache v1.12.5 // indirect
	github.com/alecthomas/participle/v2 v2.1.4 // indirect
	github.com/blastrain/vitess-sqlparser v0.0.0-20201030050434-a139afbb1aba // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/chargebee/chargebee-go v2.27.0+incompatible // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/emersion/go-sasl v0.0.0-20241020182733-b788ff22d5a6 // indirect
	github.com/emersion/go-smtp v0.22.0 // indirect
	github.com/gocql/gocql v1.7.0 // indirect
	github.com/golang-jwt/jwt/v5 v5.2.2 // indirect
	github.com/golang/snappy v1.0.0 // indirect
	github.com/google/flatbuffers v25.2.10+incompatible // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/google/wire v0.6.0 // indirect
	github.com/gorilla/mux v1.8.1 // indirect
	github.com/hailocab/go-hostpool v0.0.0-20160125115350-e80d13ce29ed // indirect
	github.com/hashicorp/golang-lru/v2 v2.0.7 // indirect
	github.com/juju/errors v1.0.0 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/robfig/cron/v3 v3.0.1 // indirect
	github.com/spf13/pflag v1.0.6 // indirect
	github.com/stretchr/objx v0.5.2 // indirect
	github.com/stretchr/testify v1.10.0 // indirect
	github.com/tetratelabs/wazero v1.9.0 // indirect
	github.com/untillpro/airc-ticket-layouts v0.0.0-20250709130943-ecf82b6564cc // indirect
	github.com/untillpro/airs-scheme v0.0.0-20250813090602-6ed97f5c8f85 // indirect
	github.com/untillpro/dynobuffers v0.0.0-20250326114643-81fd600ebf7c // indirect
	github.com/untillpro/gojay v1.2.17-0.20250325110036-70ad3373aa24 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/wneessen/go-mail v0.6.2 // indirect
	go.etcd.io/bbolt v1.4.2 // indirect
	golang.org/x/crypto v0.41.0 // indirect
	golang.org/x/exp v0.0.0-20250606033433-dcc06ee1d476 // indirect
	golang.org/x/mobile v0.0.0-20250813145510-f12310a0cfd9 // indirect
	golang.org/x/mod v0.27.0 // indirect
	golang.org/x/net v0.43.0 // indirect
	golang.org/x/sync v0.16.0 // indirect
	golang.org/x/sys v0.35.0 // indirect
	golang.org/x/text v0.28.0 // indirect
	golang.org/x/tools v0.36.0 // indirect
	gopkg.in/inf.v0 v0.9.1 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
