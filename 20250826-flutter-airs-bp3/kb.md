# Knowledge Base

## ChatGPT

Android Studio: Need a simple application that has Start and Stop buttons, these buttons manage the HTTPServer
https://chatgpt.com/c/5041855d-ed7c-4f6b-ac3b-ff27b302bd67

how to create and run a device using adb command
https://chatgpt.com/c/7a3b2336-af16-4b76-a894-98708d3ab3da

## Flutter

https://docs.flutter.dev/get-started/install/windows
flutter

https://stackoverflow.com/questions/73724659/how-to-import-aar-file-in-the-flutter-plugin

## Conf

### Defender

https://learn.microsoft.com/en-us/windows/android/defender-settings
Settings > Update & Security > Windows Security > Virus & threat protection. Under Virus & threat protection settings, select Manage settings, and then under Exclusions

- Visual Studio dev environment process: devenv.exe
- Visual Studio build process: msbuild.exe
- JetBrains directory: %LOCALAPPDATA%\JetBrains\<Transient directory (folder)>


It is recommended to make sure the following paths are added to the Defender folder exclusion list: 

C:\Users\<USER>\.gradle C:\Users\<USER>\AppData\Local\Android\Sdk
C:\Users\<USER>\AppData\Local\Google\AndroidStudio2024.1
C:\workspaces\work\inv-go\20240703-android-server\javaserver

## Button

https://www.youtube.com/watch?v=qYJyqKlHH_8

## Build

gomobile bind -androidapi 21 -target=android -o httpserver.aar .

gomobile build -target=android -androidapi 22 golang.org/x/mobile/example/basic

C:\Users\<USER>\go\pkg\mod\golang.org\x\mobile@v0.0.0-20240604190613-2782386b8afd\example\basic 

## Issues

https://github.com/golang/go/issues/23106
This is working as intended, you should use gomobile bind against a non-main package (it will create bindings for the exported symbols)

https://github.com/golang/go/issues/58661
Inspecting the built basic.apk with apktool d basic.apk reveals in ndroidManifest.xml: platformBuildVersionCode="16" (and no other API version mention).

### Misc

mklink /D C:\Users\<USER>\AppData\Local\Android\sdk\ndk-bundle C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.11902837

go get golang.org/x/mobile/bind
