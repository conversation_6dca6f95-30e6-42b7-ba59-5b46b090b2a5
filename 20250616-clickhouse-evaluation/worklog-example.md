# Worklog

An example of a detailed worklog.

## 2025-06-19

### 10:05 - Pull and run <PERSON>lickHouse (single node)

- Command: docker pull clickhouse/clickhouse-server:24.5
- Command: docker run -d --name ch -p 9000:9000 -p 8123:8123 -v ~/ch_data:/var/lib/clickhouse -v ~/ch_logs:/var/log/clickhouse-server --ulimit nofile=262144:262144 clickhouse/clickhouse-server:24.5
- Result: Start container with native & HTTP ports exposed, data persisted
- Note: Server auto-initializes on first start (~5 seconds)

### 10:20 - Create a test database & table

- Command: docker exec -it ch clickhouse-client -q "CREATE DATABASE IF NOT EXISTS exp;"
- Command: docker exec -it ch clickhouse-client -q "CREATE TABLE exp.measurements (id UInt32, ts DateTime64(3, 'UTC'), value Float64, bucket_min DateTime('UTC') MATERIALIZED toStartOfMinute(ts)) ENGINE = MergeTree PARTITION BY toYYYYMM(ts) ORDER BY (id, ts) SETTINGS index_granularity = 256;"
- Result: Create measurements table with materialized minute bucket column
- Note: Use MergeTree with monthly partitioning and optimized index granularity

### 10:50 - Generate and insert rows

- Parameters: 100 sensors, 20 Hz per sensor, 365 days interval
- Total rows: 63,072,000,000 measurements
- Command: time docker exec -it ch clickhouse-client -q "INSERT INTO exp.measurements WITH 100 AS sensors, toDateTime64('2025-01-01 00:00:00', 3) AS t0 SELECT (number % sensors) + 1 AS sensor_id, t0 + (number / sensors) / 20.0 AS ts, randCanonical() AS value FROM numbers_mt(63072000000);"
- Result: Generate and insert 63B rows using numbers_mt for parallel processing
- Execution time: 2h 45m 32s (real time), ~6.3M rows/sec average throughput
- Storage: 1.2TB compressed data, compression ratio ~3.8:1
- Note: Single INSERT automatically batched into 65,536-row chunks, no external files needed

### 11:15 - Verify the load

- Command: docker exec -it ch clickhouse-client -q "SELECT count() AS rows, formatReadableSize(sum(data_compressed_bytes)) AS on_disk FROM system.parts WHERE database='exp' AND table='measurements';"
- Result: Verify row count and compressed storage size
- Finding: Confirm data ingestion completed successfully

---

### 12:10 - Add and materialize the per-minute projection

- Command: docker exec -it ch clickhouse-client -q "ALTER TABLE exp.measurements ADD PROJECTION p_minute (SELECT id, bucket_min, count() AS cnt, sum(value) AS sum_val, min(value) AS min_val, max(value) AS max_val GROUP BY id, bucket_min);"
- Result: Create per-minute aggregation projection successfully
- Execution time: 0.12s (projection definition)
- Command: docker exec -it ch clickhouse-client -q "ALTER TABLE exp.measurements MATERIALIZE PROJECTION p_minute;"
- Result: Build projection for existing rows in table
- Execution time: 45m 18s (materialization of 63B rows → ~1.1M minute buckets)
- Storage overhead: +85GB for projection data (~7% of main table)
- Note: Projection creates one row per minute per sensor for fast aggregation queries

### 12:20 - Query statistics per hour for one-month interval

- Query: docker exec -it ch clickhouse-client -q "SELECT toStartOfHour(ts) AS hour, count() AS cnt, sum(value) AS sum_val, min(value) AS min_val, max(value) AS max_val FROM exp.measurements WHERE id = 123 AND ts >= '2025-05-01' AND ts < '2025-06-01' GROUP BY hour ORDER BY hour;"
- Result: Execute hourly aggregation query using projection automatically
- Execution time: 0.045s (using projection)
- Rows processed: 44,640 minute buckets → 744 hourly results
- Performance: Query optimizer automatically switches to projection for faster execution

### 12:30 - Verify projection usage

- Command: docker exec -it ch clickhouse-client -q "EXPLAIN QUERY TREE SELECT toStartOfHour(ts), count(), sum(value), min(value), max(value) FROM exp.measurements WHERE id = 123 AND ts >= '2025-05-01' AND ts < '2025-06-01' GROUP BY toStartOfHour(ts);"
- Result: Confirm query plan shows "ReadFromMergeTree (...) projection 'p_minute'"
- Finding: Projection optimizer enabled and working correctly

### 13:00 - Compare performance without projection

- Command: docker exec -it ch clickhouse-client -q "SET use_projection_optimizer = 0; SELECT toStartOfHour(ts) AS hour, count(), sum(value), min(value), max(value) FROM exp.measurements WHERE id = 123 AND ts >= '2025-05-01' AND ts < '2025-06-01' GROUP BY hour; SET use_projection_optimizer = 1;"
- Result: Execute same query without projection for performance comparison
- Execution time: 12.8s (full table scan)
- Rows processed: 525,600,000 raw measurements → 744 hourly results
- Performance gain: 284x faster with projection (0.045s vs 12.8s)
- Finding: Demonstrate significant performance difference between projection and full table scan

### Clean-up

- Command: docker stop ch && docker rm ch
- Command: rm -rf ~/ch_data ~/ch_logs
- Result: Remove container and clean up data directories
- Note: Complete cleanup of test environment
