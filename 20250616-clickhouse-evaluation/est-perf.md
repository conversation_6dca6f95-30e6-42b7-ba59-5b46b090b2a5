# Performance Estimation

- By o3: [Ingest time estimation](https://chatgpt.com/c/685030de-9eec-800b-b9b0-22c8bb6f08b2)

## Performance estimation

| Assumed ingest rate (rows / s) | Where that figure comes from                                                                                                      | Expected wall-clock time for 63 072 000 000 rows | Notes                                                                                            |
|--------------------------------|-----------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------|--------------------------------------------------------------------------------------------------|
| **0.6 M r/s**                  | \~4 M r/s measured on a 59-core ClickHouse Cloud node ⇒ ≈ 68 k r/s per core, scaled to 10 cores ≈ 0.6 M r/s ([clickhouse.com][1]) | **\~29 h**                                       | Very conservative; reflects disk & merge-tree overhead dominating CPU.                           |
| **1 M r/s**                    | Widely reported “reasonable” single-node rate for moderately wide tables ([github.com][2])                                        | **\~17.5 h**                                     | What you often see without special tuning on commodity NVMe.                                     |
| **2 M r/s**                    | Mux-engineering write-up: 2 M r/s sustained, 4 M r/s peaks on one server after batch-size tuning ([mux.com][3])                   | **\~8.8 h**                                      | Achievable when you: bulk-batch, set `max_insert_threads≈CPU/2`, and keep merges out of the way. |
| **5 M r/s**                    | Upper-bound you might hit on a very well-tuned 10-core box with fast PCIe 4 NVMe and large batches                                | **\~3.5 h**                                      | Requires ≈ 500 MB/s sustained disk write and >32 GiB RAM for big insert blocks.                  |

> **Rule of thumb:**
> *time (h) ≈ 17.5 ÷ (throughput in M r/s)*

---

## Why the range is wide

1. **CPU vs. merge threads**
   ClickHouse needs spare cores to merge the parts it just wrote. The tuning guide recommends using **\~½ of cores for inserts** and letting the rest merge in the background. ([clickhouse.com][1])

2. **Batch size matters**
   Network / client overhead dominates for tiny inserts. Aim for blocks of **≥5 – 10 million rows** or set

   ```sql
   SET max_insert_threads = 5,
       min_insert_block_size_rows = 0,
       min_insert_block_size_bytes = <calc>;
   ```

   The ClickHouse blog’s formula (`bytes ≈ peak_mem / (3 × threads)`) is a good starting point. ([clickhouse.com][1])

3. **Disk bandwidth & latency**
   Your table’s four columns compress to roughly 8–10 bytes/row, so 63 B rows ≈ 500 GiB on disk. NVMe that sustains ≥500 MB/s will not be the bottleneck; merges and small-part creation usually are.

---

## Practical advice for your 10-core host

| Setting                       | Suggestion                                                                              | Why                              |
|-------------------------------|-----------------------------------------------------------------------------------------|----------------------------------|
| `max_insert_threads`          | **5**                                                                                   | Leaves 5 cores free for merges.  |
| Batch size                    | **≥10 M rows / insert** (or stream via `INSERT … FORMAT` with the server batching)      | Minimises part count & fsyncs.   |
| `min_insert_block_size_bytes` | `(available RAM / 3) / max_insert_threads`                                              | Blog-recommended heuristic.      |
| Disk                          | Single fast NVMe or striped SSDs                                                        | Write-amplification from merges. |
| Network                       | If inserting remotely, compress on the client (`INSERT … FORMAT RowBinaryWithDefaults`) | Keeps CPU busy, network idle.    |

With those settings you should land **in the 1–2 M rows/s band**, i.e. **\~9–18 hours** to consume the full 63 billion-row batch on a standalone 10-core machine. If that is too slow, split the load across multiple hosts or upgrade to more cores—ClickHouse’s ingest throughput scales almost linearly with CPU when merges have room to breathe. ([clickhouse.com][1])

[1]: https://clickhouse.com/blog/supercharge-your-clickhouse-data-loads-part2 "Supercharging your large ClickHouse data loads - Tuning a large data load for speed"
[2]: https://github.com/ClickHouse/ClickHouse/issues/42374 "What is the insertion speed of clickhouse？ · Issue #42374 · ClickHouse/ClickHouse · GitHub"
[3]: https://www.mux.com/blog/latency-and-throughput-tradeoffs-of-clickhouse-kafka-table-engine "How we cut ClickHouse latency from 12s to 2s | Mux"