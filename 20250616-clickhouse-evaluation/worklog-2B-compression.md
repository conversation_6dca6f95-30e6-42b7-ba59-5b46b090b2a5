# Better compression for 2B measurements

## Abstract

This worklog documents the evaluation of ClickHouse for storing and querying 2 billion time-series measurements with optimized compression. The experiment was conducted on a 12th Gen Intel i7-12700 system (12 cores, 20 logical processors) running ClickHouse in Docker.

### Hardware Specification

- CPU: 12th Gen Intel Core i7-12700 (12 cores, 20 logical processors)
- Base Clock: 2.1 GHz
- Memory: 32GB
- Storage
  - FriendlyName    : NVMe CT4000P3PSSD8
  - MediaType       : SSD
  - Size            : 4000787030016
- OS: Windows 11  
- Environment: Docker on WSL2

### Data Generation Overview

- 2 billion synthetic temperature measurements
- 100 simulated sensors at 20Hz sampling rate
- Data model includes:
  - Deterministic per-sensor baseline offsets
  - Daily temperature cycles (5°C amplitude)
  - Seasonal drift (3°C amplitude)
  - Controlled Gaussian noise (σ=0.05)
  - Rare temperature spikes (±10°C, 0.001% probability)
- Time range: ~12 days of continuous measurements

### Key Results

- Ingestion: 2 billion rows in ~8 minutes (≈4.2M rows/second)
- Storage: 11.89 GiB compressed (≈5.6 bytes per row)
- Query performance:
  - Single sensor hourly aggregation (14-day period): 0.006 seconds
- Specialized codecs (Delta, Gorilla, ZSTD) significantly improved compression

---

## Objectives

- Now 2B measurements takes 50GB of storage
- Assess storage requirements and compression efficiency for 2 billion measurements

## Configuration

```powsh
> Get-CimInstance Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors, MaxClockSpeed, VirtualizationFirmwareEnabled

Name                          : 12th Gen Intel(R) Core(TM) i7-12700
NumberOfCores                 : 12
NumberOfLogicalProcessors     : 20
MaxClockSpeed                 : 2100
VirtualizationFirmwareEnabled : False

```

## 2025-06-20

### 10:39 - why is data so noisy?

Data is quite noisy and that can be the reason for the low compression rate

```text
gmp@DESKTOP-QL5P690:~/tasks/20250620-clickhouse$ docker exec -it ch clickhouse-client -q "
SELECT * from exp.measurements where id = 1 limit 200;
"
1       2025-01-01 00:00:00     16.276237388242876
1       2025-01-01 00:00:00     16.674918920398053
1       2025-01-01 00:00:00     17.17885780056921
1       2025-01-01 00:00:00     17.239306315264237
1       2025-01-01 00:00:00     18.00512973739021
1       2025-01-01 00:00:00     18.266708576558315
1       2025-01-01 00:00:00     18.586975589885125
1       2025-01-01 00:00:00     18.6300175723831
1       2025-01-01 00:00:00     18.84356893479965
1       2025-01-01 00:00:00     19.41107307685105
1       2025-01-01 00:00:00     20.3240583690061
1       2025-01-01 00:00:00     21.042719534999588
1       2025-01-01 00:00:00     21.51514086034313
1       2025-01-01 00:00:00     21.91673447545574
1       2025-01-01 00:00:00     21.975566855027093
1       2025-01-01 00:00:00     22.85097515871344
1       2025-01-01 00:00:00     23.314107552328938
1       2025-01-01 00:00:00     24.406768747755066
1       2025-01-01 00:00:00     24.43689472491169
1       2025-01-01 00:00:00     25.495944159201745
1       2025-01-01 00:00:01     16.14300955747104
```

- ClickHouse Playground: indeed, noisy: https://shorturl.at/DefbV
- o3👎: https://chatgpt.com/c/68551b32-83dc-800b-a4c0-122353f42b34
- opus👎: https://claude.ai/chat/a8226f5f-5a6e-4b4b-97ff-d85bd8d872b6
- Sonnet 4, Extended Thinking🎁: https://claude.ai/chat/83eee2b6-c606-4102-8b8c-ea201791a075
- Gemini 2.5 Pro🎁: https://aistudio.google.com/app/prompts/1wzd1unBd2D3zqHQDuSQSCwVgEXXbqXYF
- o4-mini-high👎: https://chatgpt.com/g/g-p-67e5636c99d08191ad4fec99f910eb82-tech/project?model=o4-mini-high

Here it is:
```sql
    + 10 * (rand(id) / 4294967295  - 0.5)                           -- per-sensor shift
```

Suggestion from the Gemini:

```bash
time docker exec -it ch clickhouse-client -q "
INSERT INTO exp.measurements
WITH
    100                                AS sensors,
    0.05                               AS step_sec,
    toDateTime64('2025-01-01', 3)      AS t0,
    5                                  AS amp_day,
    0.2                                AS sigma_noise
SELECT
    id,
    ts,
    /* ---------- temperature model ---------- */
    /* base 20 °C, sensor offset ±5 °C        */
    20
    /* Use a deterministic hash for a STABLE per-sensor shift */
    + 10 * (intHash64(id) / 18446744073709551615 - 0.5)
    /* diurnal wave: 24 h = 86 400 s */
    + amp_day * sin(2 * pi() *
          toUnixTimestamp(ts) / 86400)
    /* seasonal drift: 365 day sine, amplitude 3 °C */
    + 3 * sin(2 * pi() *
          (toUnixTimestamp(ts) / 86400) / 365)
    /* white Gaussian noise */
    + randNormal(0, sigma_noise)
    /* occasional ±10 °C spike (0.001 %) */
    + if(rand32() < 42949, (rand32() % 2) ? 10 : -10, 0)
AS value
FROM
(
    /* inner stream: exactly 2 000 000 000 rows */
    SELECT
        (number % sensors) + 1                                       AS id,
        addSeconds(t0, (number / sensors) * step_sec)                AS ts
    FROM numbers_mt(2000000000)
) AS src;
"
```
> Note: 18446744073709551615 is the maximum value for an unsigned 64-bit integer (2^64 - 1), which is the output range of intHash64. Dividing by this normalizes the hash output to a float between 0.0 and 1.0.

`sigma_noise == 0.2` seems as too noisy:

![sigma0_2](sigma0_2.png)

Let's use [0.05](https://shorturl.at/Gp8ka):

![sigma0_05](sigma0_05.png)

### 11:11 recreate data

```bash
docker stop ch && docker rm ch
sudo rm -rf ~/ch_data ~/ch_logs
```

```bash
docker run -d --name ch \
           -p 9000:9000   -p 8123:8123 \
           -v ~/ch_data:/var/lib/clickhouse \
           -v ~/ch_logs:/var/log/clickhouse-server \
           --ulimit nofile=262144:262144 \
           clickhouse/clickhouse-server:24.5
```

```bash
$ docker exec -it ch clickhouse-client -q "
CREATE DATABASE IF NOT EXISTS exp;
"

$ docker exec -it ch clickhouse-client -q "
CREATE TABLE exp.measurements
(
    id       UInt32                          CODEC(T64, ZSTD(1)),
    ts       DateTime64(0, 'UTC')            CODEC(Delta, ZSTD(1)),
    value    Float64                         CODEC(Gorilla, ZSTD(3)),

    /* helper for projections - virtual, not stored */
    bucket_min ALIAS toStartOfMinute(ts)
)
ENGINE = ReplacingMergeTree()               -- non-replicated, row-dedup
PARTITION BY toYYYYMM(ts)                   -- unique tuple, no version column
ORDER BY (id, ts, value)
SETTINGS
    index_granularity = 256,

    /* ------- INSERT-retry deduplication window (block level) ------- */
    non_replicated_deduplication_window = 1000,   -- remember 1 000 block_ids
    -- default 7-day time limit is usually fine; keep seconds setting untouched
    /* ---------------------------------------------------------------- */

    cleanup_delay_period = 60,
    vertical_merge_algorithm_min_rows_to_activate = 8192;
"

$ docker exec -it ch clickhouse-client -q "
ALTER TABLE exp.measurements
ADD PROJECTION p_minute
(
    SELECT
        id,
        bucket_min,
        count()     AS cnt,
        sum(value)  AS sum_val,
        min(value)  AS min_val,
        max(value)  AS max_val
    GROUP BY id, bucket_min
);
"

$ docker exec -it ch clickhouse-client -q "ALTER TABLE exp.measurements MATERIALIZE PROJECTION p_minute;"
```

```bash
$ time docker exec -it ch clickhouse-client -q "
INSERT INTO exp.measurements
WITH
    100                                AS sensors,
    0.05                               AS step_sec,
    toDateTime64('2025-01-01', 3)      AS t0,
    5                                  AS amp_day,
    0.05                               AS sigma_noise
SELECT
    id,
    ts,
    /* ---------- temperature model ---------- */
    /* base 20 °C, sensor offset ±5 °C        */
    20
    /* Use a deterministic hash for a STABLE per-sensor shift */
    + 10 * (intHash64(id) / 18446744073709551615 - 0.5)
    /* diurnal wave: 24 h = 86 400 s */
    + amp_day * sin(2 * pi() *
          toUnixTimestamp(ts) / 86400)
    /* seasonal drift: 365 day sine, amplitude 3 °C */
    + 3 * sin(2 * pi() *
          (toUnixTimestamp(ts) / 86400) / 365)
    /* white Gaussian noise */
    + randNormal(0, sigma_noise)
    /* occasional ±10 °C spike (0.001 %) */
    + if(rand32() < 42949, (rand32() % 2) ? 10 : -10, 0)
AS value
FROM
(
    /* inner stream: exactly 2 000 000 000 rows */
    SELECT
        (number % sensors) + 1                                       AS id,
        addSeconds(t0, (number / sensors) * step_sec)                AS ts
    FROM numbers_mt(2000000000)
) AS src;
"
```

#### Result: NSERT time

```text
real    7m53.201s
user    0m0.037s
sys     0m0.028s
```

### 11:34 - Get rows count and used disk space

```bash
docker exec -it ch clickhouse-client -q "
SELECT
    count()  AS rows,
    formatReadableSize(sum(data_compressed_bytes)) AS on_disk
FROM system.parts
WHERE database='exp' AND table='measurements';
"
```

#### Result: Disk space

```text
1336    41.75 GiB
```

### 11:41 - avg, min, max

```bash
docker exec -it ch clickhouse-client -q "
SELECT
    toStartOfHour(ts) AS hour,
    count() AS cnt,
    avg(value) AS sum_val,
    min(value) AS min_val,
    max(value) AS max_val
FROM exp.measurements
WHERE id = 1
  AND ts >= '2025-01-01'
  AND ts <  '2025-01-12'
GROUP BY hour
ORDER BY hour;
"
```

### Result: avg, min, max

```bash
2025-01-01 00:00:00     72000   22.10154210251001       21.312548902312603      22.927398379011162
2025-01-01 01:00:00     72000   23.36121100522873       22.55291041646436       32.83057691865756
2025-01-01 02:00:00     72000   24.48988623746627       23.787934897711192      25.159967871252192
2025-01-01 03:00:00     72000   25.4121800977698        15.082417920957134      25.948068493247476
2025-01-01 04:00:00     72000   26.065417574852642      25.630417207493895      26.472411982097587
2025-01-01 05:00:00     72000   26.404879022958045      26.122777052226915      26.667873896332797
2025-01-01 06:00:00     72000   26.40666663734106       26.13089472482567       26.68093082180312
2025-01-01 07:00:00     72000   26.072172276590376      25.63033845710521       26.476223553471225
2025-01-01 08:00:00     72000   25.423260533005937      24.857817456919207      25.952713962989318
2025-01-01 09:00:00     72000   24.505171405429568      23.823248324230438      34.33955643044007
2025-01-01 10:00:00     72000   23.38052265700232       22.612062993952012      33.81133537134863
2025-01-01 11:00:00     72000   22.12474652049877       21.312681016608945      22.9178432036748
2025-01-01 12:00:00     72000   20.825402838394897      20.0570463139179        21.631937606911414
2025-01-01 13:00:00     72000   19.570452427551587      18.81798369083953       20.34476548321417
2025-01-01 14:00:00     72000   18.4453635976564        17.795145116650332      19.13293623729864
2025-01-01 15:00:00     72000   17.52705056868217       17.011996549493766      18.085800720317668
2025-01-01 16:00:00     72000   16.87862517926853       6.9397442504303655      26.783950825459787
2025-01-01 17:00:00     72000   16.54345666989049       16.285824500795567      16.82488683338034
2025-01-01 18:00:00     72000   16.54524514476124       16.25409927752452       16.813582488254315
2025-01-01 19:00:00     72000   16.883841058967352      6.7493316117283015      17.314611839577758
2025-01-01 20:00:00     72000   17.537285916604898      7.40719580780198        18.128024274942977
2025-01-01 21:00:00     72000   18.45932267945464       8.191697787194837       19.187606796640953
2025-01-01 22:00:00     72000   19.58887278437729       18.814109522854487      20.370248876938387
2025-01-01 23:00:00     72000   20.847976656373366      20.05228768834516       21.648098797254313
2025-01-02 00:00:00     72000   22.151502367572185      11.646422130761245      22.957124838701034
2025-01-02 01:00:00     72000   23.410800740840518      22.6450691196533        32.94199019139083
2025-01-02 02:00:00     72000   24.539882099046395      23.867911383030936      25.204047408686947
2025-01-02 03:00:00     72000   25.46304404693707       24.889790238983107      26.013750044848777
2025-01-02 04:00:00     72000   26.115164184968044      25.691407550772634      26.512924072304024
2025-01-02 05:00:00     72000   26.4545731518489        26.154677963079767      36.43508429847695
2025-01-02 06:00:00     72000   26.456520739452834      26.140242687941264      26.697704836063867
2025-01-02 07:00:00     72000   26.12188599219766       25.625122757994852      26.536420382091688
2025-01-02 08:00:00     72000   25.473289710404476      15.61326133194855       35.66400245596469
2025-01-02 09:00:00     72000   24.55520551421092       23.86822800568702       34.160650542997914
2025-01-02 10:00:00     72000   23.42984505265058       12.97576513747481       24.172688574259087
2025-01-02 11:00:00     72000   22.17520598489869       12.10106299250711       32.14792518575777
2025-01-02 12:00:00     72000   20.87535400476392       10.42826821356319       21.670771364181952
2025-01-02 13:00:00     72000   19.62049374473307       18.866274565024387      20.388215402667214
2025-01-02 14:00:00     72000   18.494678218885365      17.81728722928365       19.171405804797445
2025-01-02 15:00:00     72000   17.576888958314743      7.757919408837676       27.93887531041875
2025-01-02 16:00:00     72000   16.928241974674293      16.548699199339957      17.346851605789766
2025-01-02 17:00:00     72000   16.593480333308552      16.347294938528734      16.880443371817382
2025-01-02 18:00:00     72000   16.595360847993405      16.32948912134076       16.88243047474312
2025-01-02 19:00:00     72000   16.93399048746313       16.520852708113175      17.402637806801412
2025-01-02 20:00:00     72000   17.587187421526767      7.710421780019587       27.56531335394379
2025-01-02 21:00:00     72000   18.50991121428785       17.83819245357949       19.197810129315563
2025-01-02 22:00:00     72000   19.638330365845352      18.84261964051158       20.406158356536633
2025-01-02 23:00:00     72000   20.89797430423522       20.11674184817029       21.686170766506653
2025-01-03 00:00:00     72000   22.20150028859991       21.408642365003516      23.00539755808493
2025-01-03 01:00:00     72000   23.460879314004515      22.715353693113336      33.795630429908755
2025-01-03 02:00:00     72000   24.5901706017311        23.890813251026426      25.251043464170007
2025-01-03 03:00:00     72000   25.51257559099529       24.9481012152956        35.2396444280011
2025-01-03 04:00:00     72000   26.16527570045498       25.72452313410923       26.551580166938646
2025-01-03 05:00:00     72000   26.504094760274192      26.230819668490827      26.745436774049075
2025-01-03 06:00:00     72000   26.50638049214977       26.23824813573547       26.753896261162136
2025-01-03 07:00:00     72000   26.17190037542827       25.719394363702374      26.596169476326885
2025-01-03 08:00:00     72000   25.523063209992625      24.927891205940615      26.06553431308599
2025-01-03 09:00:00     72000   24.60496042204765       23.9001864003734        34.92395241684146
2025-01-03 10:00:00     72000   23.479456286873397      13.790670500189318      33.444550349635826
2025-01-03 11:00:00     72000   22.22481236003918       12.834362553641874      31.80885298232791
2025-01-03 12:00:00     72000   20.925285211947646      20.144860595630764      21.72219599357265
2025-01-03 13:00:00     72000   19.669501465101686      9.299126657005736       30.11937645996679
2025-01-03 14:00:00     72000   18.545037797930018      17.874910536587784      28.420969498270892
2025-01-03 15:00:00     72000   17.62665368326694       7.270076541628136       27.493012801279466
2025-01-03 16:00:00     72000   16.977666556072155      16.580309374284578      17.443583414211957
2025-01-03 17:00:00     72000   16.64304134416617       16.378474261366698      16.935518150463057
2025-01-03 18:00:00     72000   16.644861825931073      16.400988960036702      16.928327451653733
2025-01-03 19:00:00     72000   16.98378454343237       16.584452646323008      17.407663152629162
2025-01-03 20:00:00     72000   17.63669663980131       17.090517928117663      18.199925122165205
2025-01-03 21:00:00     72000   18.55939078018307       17.871804976976616      28.056535939457582
2025-01-03 22:00:00     72000   19.687923074228475      18.939172330396577      29.306180941655878
2025-01-03 23:00:00     72000   20.94731306571594       20.15826190658031       31.24707538686939
2025-01-04 00:00:00     72000   22.250828810856422      21.414166476454188      23.05955703337042
2025-01-04 01:00:00     72000   23.51093028190787       22.698992013779357      33.85658081699721
2025-01-04 02:00:00     72000   24.639708097874646      23.951264199968364      25.309658836238096
2025-01-04 03:00:00     72000   25.562461459357323      24.979661146332703      35.84701457587285
2025-01-04 04:00:00     72000   26.214725529885605      16.16280278388916       36.31232337536545
2025-01-04 05:00:00     72000   26.553888101966777      16.46697426737352       26.827746034522526
2025-01-04 06:00:00     72000   26.55614125819666       26.244819941348723      36.654136602554644
2025-01-04 07:00:00     72000   26.221168045792613      25.759190291021028      26.612002330312198
2025-01-04 08:00:00     72000   25.572633697781452      24.972840854166762      26.136706411105
2025-01-04 09:00:00     72000   24.654411928680695      23.96107326846193       25.302535625814595
2025-01-04 10:00:00     72000   23.529155205583 22.77274463721288       24.297600913653618
2025-01-04 11:00:00     72000   22.27393049052616       21.473112901412417      23.0629531747359
2025-01-04 12:00:00     72000   20.97448300745629       20.1686986720112        21.77405979178253
2025-01-04 13:00:00     72000   19.719440952104577      18.992446199829644      20.485043306476577
2025-01-04 14:00:00     72000   18.594291453894265      8.579501666628904       19.282961415955924
2025-01-04 15:00:00     72000   17.67629156522569       17.12663100839236       18.246634813601727
2025-01-04 16:00:00     72000   17.027224241574785      16.62265268499176       17.481311785831476
2025-01-04 17:00:00     72000   16.692220996255216      16.414775127889254      26.561164393036755
2025-01-04 18:00:00     72000   16.694268963884863      16.45234474497655       16.96315402156328
2025-01-04 19:00:00     72000   17.033399974203952      16.65842752522184       17.450803949049845
2025-01-04 20:00:00     72000   17.685960589380457      17.139564322976927      18.291033574006267
2025-01-04 21:00:00     72000   18.60863317428536       17.930013102500318      19.32988760650716
2025-01-04 22:00:00     72000   19.737389080566864      9.437637877196138       20.485525060184465
2025-01-04 23:00:00     72000   20.996701937884687      11.491287801555032      30.61708795073381
2025-01-05 00:00:00     72000   22.300729860482893      21.494763290136174      32.61588427805843
2025-01-05 01:00:00     72000   23.559850590596234      22.788654952418387      33.561601265317904
2025-01-05 02:00:00     72000   24.688616538108356      14.938686191150499      25.358045202422616
2025-01-05 03:00:00     72000   25.611558406163795      25.02998884046528       26.153121628217267
2025-01-05 04:00:00     72000   26.26452858940837       25.814762337852414      36.49759528049837
2025-01-05 05:00:00     72000   26.60347674534461       26.328595300542062      36.649207115550595
2025-01-05 06:00:00     72000   26.60510709712417       26.326732276634147      26.855593741304002
2025-01-05 07:00:00     72000   26.27033465444615       25.828319732696137      26.665663764555976
2025-01-05 08:00:00     72000   25.621901195332985      25.053210532983698      26.156825769117948
2025-01-05 09:00:00     72000   24.703528453605184      14.346390142904564      25.396141858633936
2025-01-05 10:00:00     72000   23.578575349705435      22.81873239710936       24.315029648289837
2025-01-05 11:00:00     72000   22.323326077850385      21.537195042925568      31.810030021973397
2025-01-05 12:00:00     72000   21.02396296016595       20.206400425925825      21.85051101870861
2025-01-05 13:00:00     72000   19.768858034147307      10.24092303452409       29.758464492763014
2025-01-05 14:00:00     72000   18.643599547338226      17.974084343201394      19.345702966003746
2025-01-05 15:00:00     72000   17.725147283010948      17.1279783600015        18.28779033010022
2025-01-05 16:00:00     72000   17.076135312586217      16.694368306420383      17.5250468148667
2025-01-05 17:00:00     72000   16.74125783786019       16.481155151613372      17.03971053507724
2025-01-05 18:00:00     72000   16.74358478560746       16.471664078015916      17.04026067424226
2025-01-05 19:00:00     72000   17.08238622105605       16.66402375524779       17.51103584835073
2025-01-05 20:00:00     72000   17.735177474524942      17.20366325980554       18.324199832844517
2025-01-05 21:00:00     72000   18.657338925609444      17.997880942740682      19.3521734565048
2025-01-05 22:00:00     72000   19.78660094452863       9.847856878961107       20.540636013692488
2025-01-05 23:00:00     72000   21.045665219044988      10.766617393768435      31.5278146313579
2025-01-06 00:00:00     72000   22.34900805883719       12.10779266324229       23.12346577852198
2025-01-06 01:00:00     72000   23.60854809191205       13.693961030834835      24.37883546019737
2025-01-06 02:00:00     72000   24.737767195491184      24.03667279390034       25.435686110770387
2025-01-06 03:00:00     72000   25.66050175955228       25.095304369018134      35.77942553424785
2025-01-06 04:00:00     72000   26.31284907090747       16.071988642622486      26.69810195994599
2025-01-06 05:00:00     72000   26.652260295525313      26.363092447156777      26.88979038468486
2025-01-06 06:00:00     72000   26.654012317568622      16.571050735468834      36.647585883123014
2025-01-06 07:00:00     72000   26.319254910667716      25.873284976252663      26.708125983433348
2025-01-06 08:00:00     72000   25.670291499751148      15.796826563457131      26.203827906113666
2025-01-06 09:00:00     72000   24.75256861949254       24.054816967571178      25.415069577157208
2025-01-06 10:00:00     72000   23.627104908329656      22.84611613980217       24.39552191922433
2025-01-06 11:00:00     72000   22.372314538388476      13.051155556072992      32.56205830785166
2025-01-06 12:00:00     72000   21.072461288261984      11.376827652340438      21.869459195773416
2025-01-06 13:00:00     72000   19.817296983481125      9.708743709070465       20.595982327033994
2025-01-06 14:00:00     72000   18.69214216532932       18.01390188465366       19.38571887588757
2025-01-06 15:00:00     72000   17.773651869192545      7.755620890359481       18.35329176016171
2025-01-06 16:00:00     72000   17.125396409547168      16.732379498254982      27.21271712675803
2025-01-06 17:00:00     72000   16.790026052025844      6.801040486839472       17.100812708326682
2025-01-06 18:00:00     72000   16.79242689216928       6.741695878833934       17.100766597058453
2025-01-06 19:00:00     72000   17.131303603164955      16.74069801452005       17.559495213680318
2025-01-06 20:00:00     72000   17.78435910691345       17.256468376643948      27.736252649253984
2025-01-06 21:00:00     72000   18.706236093428696      8.594067242526645       28.303240866912112
2025-01-06 22:00:00     72000   19.835539852169724      19.081281392106487      20.61523945453516
2025-01-06 23:00:00     72000   21.0949492461429        20.301816017748255      31.339059899593973
2025-01-07 00:00:00     72000   22.39823591513483       21.58952078899178       32.719548674041775
2025-01-07 01:00:00     72000   23.65759377284163       22.89144946121097       24.4340942587601
2025-01-07 02:00:00     72000   24.786519285977338      15.110198551884093      25.444986873569324
2025-01-07 03:00:00     72000   25.70867334792229       25.133000931711468      26.254233621790352
2025-01-07 04:00:00     72000   26.361779534260116      25.917054677893336      26.75763435537511
2025-01-07 05:00:00     72000   26.700978316018276      26.399927827161697      26.957399138929294
2025-01-07 06:00:00     72000   26.70276064851204       16.893153092481107      26.955686884249538
2025-01-07 07:00:00     72000   26.36785629759422       25.900566294184124      36.24588096513486
2025-01-07 08:00:00     72000   25.719251082531294      25.15744062010038       26.28331010728089
2025-01-07 09:00:00     72000   24.80109341163661       14.856981538775415      25.430898790281855
2025-01-07 10:00:00     72000   23.675720052670233      22.90160370495864       24.405782794275577
2025-01-07 11:00:00     72000   22.420929380336137      21.6123280070118        23.21584052040603
2025-01-07 12:00:00     72000   21.121212132365866      11.391393947377622      21.93141609072473
2025-01-07 13:00:00     72000   19.86619999597571       19.11806314380403       29.689816959349123
2025-01-07 14:00:00     72000   18.74082253516395       18.080785001659894      19.437543955272005
2025-01-07 15:00:00     72000   17.82277208829318       7.809540326268273       27.576099493540845
2025-01-07 16:00:00     72000   17.173875102173564      16.781850205542444      17.610084292797772
2025-01-07 17:00:00     72000   16.839319276144487      16.576724681074047      26.875906367277484
2025-01-07 18:00:00     72000   16.84117518167292       16.584968553204614      26.862233327549262
2025-01-07 19:00:00     72000   17.179679920837657      16.773429514698076      17.61265818822981
2025-01-07 20:00:00     72000   17.832589032443092      17.300629902297473      18.407846568721084
2025-01-07 21:00:00     72000   18.754714896703028      18.116593202748078      19.4443028963375
2025-01-07 22:00:00     72000   19.883634379442018      9.888354943262502       20.64855280543172
2025-01-07 23:00:00     72000   21.143314851031704      10.61023159182303       30.613045576030157
2025-01-08 00:00:00     72000   22.446513186284715      12.258817639691742      23.25139266213387
2025-01-08 01:00:00     72000   23.706049230786682      22.91855025675496       24.461241332997183
2025-01-08 02:00:00     72000   24.83477500009037       14.606917523823459      25.493159175561388
2025-01-08 03:00:00     72000   25.75736699220236       25.188897278889662      26.28818681919853
2025-01-08 04:00:00     72000   26.410067794333006      25.993499559796618      26.83196002636628
2025-01-08 05:00:00     72000   26.748958812864302      26.477827804056496      26.984915455704357
2025-01-08 06:00:00     72000   26.75108051840706       26.436182061208658      26.99647383421449
2025-01-08 07:00:00     72000   26.41643693865887       25.973356853114066      36.478056200527824
2025-01-08 08:00:00     72000   25.76807939956191       25.20208026327883       26.324853809746973
2025-01-08 09:00:00     72000   24.849138499434854      24.17800294681882       35.35952916698679
2025-01-08 10:00:00     72000   23.72379132231579       13.218058670711244      33.4467624602472
2025-01-08 11:00:00     72000   22.469155112536185      21.662926021398302      23.247479310982055
2025-01-08 12:00:00     72000   21.169642776975824      20.369443298694186      30.690453091560922
2025-01-08 13:00:00     72000   19.914260414725188      19.166122413214442      20.686060486822797
2025-01-08 14:00:00     72000   18.78924268793056       18.120175614677308      19.47176757710179
2025-01-08 15:00:00     72000   17.870846427718337      17.319351477565927      18.439353155343035
2025-01-08 16:00:00     72000   17.22201320278074       16.838118017936527      17.673429059493632
2025-01-08 17:00:00     72000   16.887127937397413      16.635168460350084      17.18291399676023
2025-01-08 18:00:00     72000   16.889158507613647      16.647914571121824      26.81308153901769
2025-01-08 19:00:00     72000   17.22751035297615       7.094710586541254       17.657840886640006
2025-01-08 20:00:00     72000   17.880449929806115      17.351673755765535      18.47090044923342
2025-01-08 21:00:00     72000   18.80345929641438       18.12387026962125       29.38636143055402
2025-01-08 22:00:00     72000   19.931930744676286      19.19778344472103       20.710554666582
2025-01-08 23:00:00     72000   21.19118275001336       11.43792891253786       21.985822046165165
2025-01-09 00:00:00     72000   22.494921665589963      21.652689345335318      32.76182393161943
2025-01-09 01:00:00     72000   23.753985565470497      23.00379596533791       24.50247381417021
2025-01-09 02:00:00     72000   24.88314635729246       24.16940489977627       35.15861909855521
2025-01-09 03:00:00     72000   25.805274884284987      25.229240409012082      26.345108696788113
2025-01-09 04:00:00     72000   26.458180623345857      26.02931746918784       36.36166828937978
2025-01-09 05:00:00     72000   26.79761733727314       26.507335555582042      36.819586607641774
2025-01-09 06:00:00     72000   26.799123102304844      26.498137968853037      27.0525596978778
2025-01-09 07:00:00     72000   26.464539139426442      26.03962586017342       36.26633685273843
2025-01-09 08:00:00     72000   25.815671022816023      25.2314350866743        26.361114423866333
2025-01-09 09:00:00     72000   24.897336922745712      24.1950481802958        25.56712473959691
2025-01-09 10:00:00     72000   23.772098033265145      14.235200800345876      24.542766396926243
2025-01-09 11:00:00     72000   22.516975688438276      21.72586568380939       23.30069430827505
2025-01-09 12:00:00     72000   21.2172845701188        11.5439221127526        31.668436500829042
2025-01-09 13:00:00     72000   19.96228967236538       19.196231576071686      20.712599209961237
2025-01-09 14:00:00     72000   18.836891914737105      18.183921038225126      19.52690330185339
2025-01-09 15:00:00     72000   17.918530905286147      17.387903026623544      18.51487566163594
2025-01-09 16:00:00     72000   17.27034759336124       16.876369228614394      27.17702757308408
2025-01-09 17:00:00     72000   16.935087795165938      16.653491619936666      26.96175951742715
2025-01-09 18:00:00     72000   16.937143916703157      16.681649234634175      17.243284114359138
2025-01-09 19:00:00     72000   17.275797427035673      16.88204937912321       27.10550678297343
2025-01-09 20:00:00     72000   17.92856454202331       17.40950375336335       18.556234941084483
2025-01-09 21:00:00     72000   18.85085514608995       18.190444922343506      28.607468129608815
2025-01-09 22:00:00     72000   19.979723346259416      9.443382710146441       20.762799136463563
2025-01-09 23:00:00     72000   21.23904020898101       20.42249546277382       30.69906627897722
2025-01-10 00:00:00     72000   22.542315801900045      12.53569860184696       23.36448410598108
2025-01-10 01:00:00     72000   23.801857310663753      13.353955294134167      33.297593611818805
2025-01-10 02:00:00     72000   24.931066249659377      24.238162596930817      25.605370560610744
2025-01-10 03:00:00     72000   25.8532756060312        15.583169090837313      26.393718259562803
2025-01-10 04:00:00     72000   26.506264497267082      26.071149469977886      36.26986859250789
2025-01-10 05:00:00     72000   26.845154364572817      26.55014357874491       27.098376377747453
2025-01-10 06:00:00     72000   26.846872102724234      26.568901295111022      27.10959961871846
2025-01-10 07:00:00     72000   26.512083639628983      16.560503709739578      26.929604048566357
2025-01-10 08:00:00     72000   25.863421999632305      15.89304651486755       26.387763048438078
2025-01-10 09:00:00     72000   24.945288131065734      24.250292682259154      25.65628564095436
2025-01-10 10:00:00     72000   23.819826686449208      14.195906924417248      24.606750983240293
2025-01-10 11:00:00     72000   22.564413005910147      21.7410385418821        23.38505455508688
2025-01-10 12:00:00     72000   21.265333947976007      20.48854478495815       31.43765205616841
2025-01-10 13:00:00     72000   20.009701827919596      19.282113342478652      20.789455420602906
2025-01-10 14:00:00     72000   18.885168734242246      18.242581549312717      29.164266398278116
2025-01-10 15:00:00     72000   17.966619743750122      17.44623866354426       18.554362045296454
2025-01-10 16:00:00     72000   17.317670483227072      16.93369540873178       17.778490082438754
2025-01-10 17:00:00     72000   16.982544661725075      16.73728602803902       17.26220171793391
2025-01-10 18:00:00     72000   16.984414435974 7.127888845250286       26.93084383888721
2025-01-10 19:00:00     72000   17.323574324251954      16.94399994193889       17.78139923529205
2025-01-10 20:00:00     72000   17.975522357995853      17.456476828259596      18.554273365292236
2025-01-10 21:00:00     72000   18.898262073913322      9.28168142460282        19.60898740480327
2025-01-10 22:00:00     72000   20.027493753344654      19.293649558036904      30.676852182693406
2025-01-10 23:00:00     72000   21.28655816833205       20.497958596995815      22.105790750986277
2025-01-11 00:00:00     72000   22.5899730751364        12.22158065645408       23.39277343966382
2025-01-11 01:00:00     72000   23.84906944751004       14.027095107079795      24.603953432642086
2025-01-11 02:00:00     72000   24.97815806617261       24.29614168308543       25.641994516311968
2025-01-11 03:00:00     72000   25.90050900887304       25.297832568253014      26.42928629927393
2025-01-11 04:00:00     72000   26.553318958026967      26.092514863040766      26.950908213730287
2025-01-11 05:00:00     72000   26.89253189714961       26.622121808277505      37.00380998132779
2025-01-11 06:00:00     72000   26.894413199161065      16.944827499997743      27.14481083429023
2025-01-11 07:00:00     72000   26.55963539144297       26.110472691820107      26.941062548880872
2025-01-11 08:00:00     72000   25.910655057339028      25.31052034574943       26.452169523934092
2025-01-11 09:00:00     72000   24.992370402765385      24.298619288861047      25.672005599417883
2025-01-11 10:00:00     72000   23.86743305160992       13.680694077514318      33.532207223094844
2025-01-11 11:00:00     72000   22.61224859032236       21.81754349183311       32.92567048026212
2025-01-11 12:00:00     72000   21.312361603214615      20.528818707425998      22.137847091663673
2025-01-11 13:00:00     72000   20.05717962309052       19.301924793318744      20.817429072063707
2025-01-11 14:00:00     72000   18.932320823563735      18.23663618215763       28.686579800211284
2025-01-11 15:00:00     72000   18.01351609363755       17.450919047850565      18.61496546178217
2025-01-11 16:00:00     72000   17.364867153462882      16.930748963635907      17.793827597962796
2025-01-11 17:00:00     72000   17.030144679621284      16.784290810665283      27.093329850959464
2025-01-11 18:00:00     72000   17.031972209031576      16.776063713527623      17.324483548631374
2025-01-11 19:00:00     72000   17.37045586148874       16.990227012735847      17.836605811558446
2025-01-11 20:00:00     72000   18.02329041185055       17.48423559887438       18.63769123868215
2025-01-11 21:00:00     72000   18.945270736389677      8.68968615514979        28.66118417591422
2025-01-11 22:00:00     72000   20.07446343468938       19.316549706937362      20.876024406569893
2025-01-11 23:00:00     72000   21.33341416601603       20.5531393467874        22.140671195335837
```

### 11:48 profile time, single sensor

```bash
$ docker exec -it ch clickhouse-client --time -q "
SELECT
    toStartOfHour(ts) AS hour,
    count() AS cnt,
    avg(value) AS sum_val,
    min(value) AS min_val,
    max(value) AS max_val
FROM exp.measurements
WHERE id = 1
  AND ts >= '2025-01-01'
  AND ts <  '2025-01-15'
GROUP BY hour
ORDER BY hour;
"
...
0.006
```

#### Result: single sensor

0.006

### 11:51 profile time, all sensors

```bash
$ docker exec -it ch clickhouse-client --time -q "
SELECT
    toStartOfHour(ts) AS hour,
    count() AS cnt,
    avg(value) AS sum_val,
    min(value) AS min_val,
    max(value) AS max_val
FROM exp.measurements
WHERE 
  ts >= '2025-01-01'
  AND ts <  '2025-01-15'
GROUP BY hour
ORDER BY hour;
"
...
8.629
```

#### Result: all sensors

8.629

### 11:51 profile time, all sensors, no avg

```bash
$ docker exec -it ch clickhouse-client --time -q "
SELECT
    toStartOfHour(ts) AS hour,
    count() AS cnt,
    sum(value) AS sum_val,
    min(value) AS min_val,
    max(value) AS max_val
FROM exp.measurements
WHERE 
  ts >= '2025-01-01'
  AND ts <  '2025-01-15'
GROUP BY hour
ORDER BY hour;
"
8.641
```

### 11:54 Add per-minute projection for all sensors

```bash
$ docker exec -it ch clickhouse-client -q "
ALTER TABLE exp.measurements
ADD PROJECTION p_minute_all
(
    SELECT
        id,
        bucket_min,
        count()     AS cnt,
        avg(value)  AS avg_val,
        sum(value)  AS sum_val,
        min(value)  AS min_val,
        max(value)  AS max_val
    GROUP BY id, bucket_min
);
"
```

```bash
$ time docker exec -it ch clickhouse-client -q "ALTER TABLE exp.measurements MATERIALIZE PROJECTION p_minute_all;"

real    0m0.200s
user    0m0.014s
sys     0m0.014s
```

```bash
$ docker exec -it ch clickhouse-client --time -q "
SELECT
    toStartOfHour(ts) AS hour,
    count() AS cnt,
    sum(value) AS sum_val,
    min(value) AS min_val,
    max(value) AS max_val
FROM exp.measurements
WHERE 
  ts >= '2025-01-01'
  AND ts <  '2025-01-15'
GROUP BY hour
ORDER BY hour;
"
...
14.956
```

#### Result: per-minute projection for all sensors

- Command for materialization is fast
- But materialization happens in background and took a lot of time (guess it from the CPU usage)
- Query speed has not been improved

### 12:29 perhaps after merge: get rows count and used disk space

After half an hour of the 25% CPU usage:

```bash
$ docker exec -it ch clickhouse-client -q "
SELECT
    count()  AS rows,
    formatReadableSize(sum(data_compressed_bytes)) AS on_disk
FROM system.parts
WHERE database='exp' AND table='measurements';
"
11      11.89 GiB

$ docker exec -it ch clickhouse-client -q "SELECT count(*) from exp.measurements"
2000000000

$ ~$ sudo du -sh ch_data
13G     ch_data

$ sudo du -sh ch_logs
425M    ch_logs
```

#### Result: after merge

- Perhaps compression happens during merges

### 12:41 - speed up all-sensors aggregation

- https://claude.ai/chat/3319375e-6245-43e7-89aa-f7f54384a1dd

```bash
$ docker exec -it ch clickhouse-client -q "
ALTER TABLE exp.measurements
ADD PROJECTION p_hourly_all
(
    SELECT
        toStartOfHour(ts) AS hour,
        count()     AS cnt,
        avg(value)  AS avg_val,
        min(value)  AS min_val,
        max(value)  AS max_val
    GROUP BY hour
);
"
```

```bash
$ docker exec -it ch clickhouse-client --time -q "
SELECT
    toStartOfHour(ts) AS hour,
    count() AS cnt,
    avg(value) AS avg_val,
    min(value) AS min_val,
    max(value) AS max_val
FROM exp.measurements
WHERE 
  ts >= '2025-01-01'
  AND ts <  '2025-01-15'
GROUP BY hour
ORDER BY hour;
"
...
14.956
```

```bash
time docker exec -it ch clickhouse-client -q "ALTER TABLE exp.measurements MATERIALIZE PROJECTION p_hourly_all;"

real    0m0.230s
user    0m0.014s
sys     0m0.027s
```

```bash
$ docker exec -it ch clickhouse-client -q "
SELECT
    name,
    rows,
    data_compressed_bytes,
    formatReadableSize(data_compressed_bytes) AS size
FROM system.projection_parts
WHERE database = 'exp' AND table = 'measurements' AND name = 'p_hourly_all';
"
p_hourly_all    2       209     209.00 B
p_hourly_all    2       207     207.00 B
p_hourly_all    2       207     207.00 B
p_hourly_all    1       166     166.00 B
p_hourly_all    1       166     166.00 B
p_hourly_all    1       166     166.00 B
p_hourly_all    1       166     166.00 B
```

### 15:49 - kill all mutations

```bash
docker exec -it ch clickhouse-client -q "
SELECT mutation_id, command, is_done, parts_to_do, latest_failed_part, latest_fail_reason
FROM system.mutations 
WHERE table = 'measurements';
"

docker exec -it ch clickhouse-client -q "
KILL MUTATION WHERE mutation_id = 'mutation_1802.txt';
"
```

### 15:50 - recreate the projection

```bash
docker exec -it ch clickhouse-client -q "
ALTER TABLE exp.measurements
ADD PROJECTION p_hourly_all
(
    SELECT
        toStartOfHour(ts) AS hour,
        count()     AS cnt,
        avg(value)  AS avg_val,
        min(value)  AS min_val,
        max(value)  AS max_val
    GROUP BY hour
);
"

time docker exec -it ch clickhouse-client -q "
ALTER TABLE exp.measurements MATERIALIZE PROJECTION p_hourly_all;
"
```

```bash
$ docker exec -it ch clickhouse-client -q "
SELECT mutation_id, command, is_done, parts_to_do, latest_failed_part, latest_fail_reason
FROM system.mutations
WHERE table = 'measurements';
"
mutation_1803.txt       MATERIALIZE PROJECTION p_hourly_all     0       2
```

```bash
$  docker exec -it ch clickhouse-client -q "
SELECT mutation_id, command, is_done, parts_to_do, latest_failed_part, latest_fail_reason
FROM system.mutations
WHERE table = 'measurements';
"
mutation_1803.txt       MATERIALIZE PROJECTION p_hourly_all     1       0
```

Check that projection is now being used:
```bash
$ docker exec -it ch clickhouse-client -q "
EXPLAIN indexes = 1
SELECT
    toStartOfHour(ts) AS hour,
    count() AS cnt,
    avg(value) AS avg_val,
    min(value) AS min_val,
    max(value) AS max_val
FROM exp.measurements
WHERE ts >= '2025-01-01' AND ts < '2025-01-15'
GROUP BY hour
ORDER BY hour;
"

Expression (Project names)
  Sorting (Sorting for ORDER BY)
    Expression ((Before ORDER BY + Projection))
      Aggregating
        Expression (Before GROUP BY)
          Expression
            ReadFromMergeTree (exp.measurements)
            Indexes:
              MinMax
                Keys:
                  ts
                Condition: and((ts in (-Inf, \'1736899200\')), (ts in [\'1735689600\', +Inf)))
                Parts: 11/11
                Granules: 7812506/7812506
              Partition
                Keys:
                  toYYYYMM(ts)
                Condition: and((toYYYYMM(ts) in (-Inf, 202501]), (toYYYYMM(ts) in [202501, +Inf)))
                Parts: 11/11
                Granules: 7812506/7812506
              PrimaryKey
                Keys:
                  ts
                Condition: and((ts in (-Inf, \'1736899200\')), (ts in [\'1735689600\', +Inf)))
                Parts: 11/11
                Granules: 7812506/7812506
```

### 16:00 why is the projection not used?

- https://claude.ai/chat/2436fac6-0cd1-43b6-9634-278a29e57b0b
- https://aistudio.google.com/app/prompts?state=%7B%22ids%22:%5B%221_QO7j5pmkSTwvkb4ODeO4T-ImyHRCrq1%22%5D,%22action%22:%22open%22,%22userId%22:%22104404457665630593195%22,%22resourceKeys%22:%7B%7D%7D&usp=sharing

### Result

- LLMs failed

### 16:18 - query the latest value

```bash
$ time docker exec -it ch clickhouse-client -q "
SELECT 
    id AS sensor_id,
    argMax(ts, ts) AS latest_timestamp,
    argMax(value, ts) AS latest_value
FROM exp.measurements
GROUP BY id
ORDER BY id;
"
...
88      2025-01-12 13:46:39     14.308835676669448
89      2025-01-12 13:46:39     14.102499526722546
90      2025-01-12 13:46:39     16.327703261808974
91      2025-01-12 13:46:39     14.84218874197584
92      2025-01-12 13:46:39     22.928005792779715
93      2025-01-12 13:46:39     14.72048677787604
94      2025-01-12 13:46:39     20.859298821510098
95      2025-01-12 13:46:39     16.360815682110168
96      2025-01-12 13:46:39     23.514142382945018
97      2025-01-12 13:46:39     23.020585275804834
98      2025-01-12 13:46:39     19.0318060526561
99      2025-01-12 13:46:39     17.17374911786927
100     2025-01-12 13:46:39     22.425355891664196

real    0m5.284s
user    0m0.015s
sys     0m0.019s
```
