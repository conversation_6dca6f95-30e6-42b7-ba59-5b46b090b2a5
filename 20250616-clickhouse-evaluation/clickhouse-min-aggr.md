# ClickHouse per minute aggregation

Based on o3 model suggestions:

- Excerpts from the [ClickHouse per minute aggregation](https://chatgpt.com/share/68501c59-2218-800b-a447-a02c94b7e971)
- [Review1](https://chatgpt.com/c/685026fc-3fe0-800b-95f4-d319bf68b05d)
- [Ingest time estimation](https://chatgpt.com/c/685030de-9eec-800b-b9b0-22c8bb6f08b2)
- [Review2](https://chatgpt.com/c/685026fc-3fe0-800b-95f4-d319bf68b05d)
- [Schema optimization](https://chatgpt.com/share/685434b5-2c3c-800b-ab07-427b17b127c0)
  - [Not shared link](https://chatgpt.com/c/68542f94-bc9c-800b-9039-e581f7a414a5)
- [Data ingestion](https://chatgpt.com/c/68542f94-bc9c-800b-9039-e581f7a414a5)

## Experiments parameters (Large)

- 100 20 Hz sensors
  - 2_000 measurements per second
- Interval: 365 days
- Number of rows: 63_072_000_000

Estimations

- [perf](est-perf.md)
- [size](est-size.md)

## Install on single-node and ingest rows

### Host prerequisites

| Item | Minimum                                          |
|------|--------------------------------------------------|
| OS   | Any Linux + Docker 20.10+                        |
| HW   | ≥ 4 vCPU, 16 GB RAM, NVMe / fast SSD (≈300 MB/s) |
| Disk | ≥ 600 GB free                                    |

---

### Pull and run ClickHouse (single node)

```bash
# 1. Latest LTS image (amd64)
docker pull clickhouse/clickhouse-server:24.5

# 2. Start container, expose native & HTTP ports, persist data
docker run -d --name ch \
           -p 9000:9000   -p 8123:8123 \
           -v ~/ch_data:/var/lib/clickhouse \
           -v ~/ch_logs:/var/log/clickhouse-server \
           --ulimit nofile=262144:262144 \
           clickhouse/clickhouse-server:24.5
```

*The server auto-initialises on the first start (≈5 s).*

---

### Create a test database & table

```bash
docker exec -it ch clickhouse-client -q "
CREATE DATABASE IF NOT EXISTS exp;

CREATE TABLE exp.measurements
(
    id      UInt32,
    ts      DateTime64(3, 'UTC'),
    value   Float64,

    bucket_min DateTime('UTC') MATERIALIZED toStartOfMinute(ts)
) ENGINE = MergeTree
  PARTITION BY toYYYYMM(ts)
  ORDER BY (id, ts)
  SETTINGS index_granularity = 256;
"
```

---

### Generate and insert rows

ClickHouse can synthesise data itself, no external script required:

```bash
time docker exec -it ch clickhouse-client -q "
INSERT INTO exp.measurements
WITH
    100                                            AS sensors,   -- adjust
    toDateTime64('2025-01-01 00:00:00', 3)         AS t0
SELECT
    (number % sensors) + 1                         AS sensor_id,         -- 1…N
    t0 + (number / sensors) / 20.0                 AS ts,               -- keeps 20 Hz per id
    randCanonical()                                AS value
FROM numbers_mt(<total-rows>);                                             
"
```

* `numbers_mt` uses all available cores; no RAM blow-up (streamed in blocks of 65 536 rows).*
* **Single INSERT** is automatically broken into 65 536-row batches; nothing extra to script.
* **numbers\_mt()** keeps CPU busy and avoids generating an external file.
* MergeTree defaults are fine for an experiment; no replication/zoo-keeper needed.

---

### Verify the load

```bash
docker exec -it ch clickhouse-client -q "
SELECT
    count()  AS rows,
    formatReadableSize(sum(data_compressed_bytes)) AS on_disk
FROM system.parts
WHERE database='exp' AND table='measurements';
"
```

---

### Clean-up

```bash
docker stop ch && docker rm ch
rm -rf ~/ch_data ~/ch_logs
```

---

## Add and materialize the **per-minute projection**

### Create the projection

```sql
-- Create the projection (one row = one minute)
ALTER TABLE exp.measurements
ADD PROJECTION p_minute
(
    SELECT
        id,
        bucket_min,            -- already computed by the MATERIALIZED column
        count() AS cnt,
        sum(value) AS sum_val,
        min(value) AS min_val,
        max(value) AS max_val
    GROUP BY id, bucket_min
);

-- Build the projection for the rows that are already in the table
ALTER TABLE exp.measurements
MATERIALIZE PROJECTION p_minute;
```

---

### Query "statistics per hour" for a one-month interval

> The SQL is identical whether the projection is used or not—ClickHouse's optimizer
> will automatically switch to the projection (enabled by default).

```sql
SELECT
    toStartOfHour(ts) AS hour,
    count() AS cnt,
    sum(value) AS sum_val,
    min(value) AS min_val,
    max(value) AS max_val
FROM exp.measurements
WHERE id = 123
  AND ts >= '2025-05-01'
  AND ts <  '2025-06-01'
GROUP BY hour
ORDER BY hour;
```

Check which plan was chosen:

```sql
EXPLAIN QUERY TREE
SELECT 
    toStartOfHour(ts), 
    count(),
    sum(value),
    min(value),
    max(value)
FROM exp.measurements
WHERE id = 123
  AND ts >= '2025-05-01'
  AND ts <  '2025-06-01'
GROUP BY toStartOfHour(ts);
```

In the output you should see
`ReadFromMergeTree (…) projection 'p_minute'`.

---

### Run the same query **without** the projection (for comparison)

```sql
SET use_projection_optimizer = 0;      -- temporarily disable projections

SELECT
    toStartOfHour(ts) AS hour,
    count(),
    sum(value),
    min(value),
    max(value)
FROM exp.measurements
WHERE id = 123
  AND ts >= '2025-05-01'
  AND ts <  '2025-06-01'
GROUP BY hour;

SET use_projection_optimizer = 1;      -- turn it back on
```
