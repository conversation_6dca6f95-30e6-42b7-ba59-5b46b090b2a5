# ClickHouse tips and tricks

- docker exec -i ch clickhouse-client --multiquery < p.sql

## Wait for system merges

```bash
docker exec -it ch clickhouse-client -q "SELECT * FROM system.merges;"
```

## Check which parts are missing projections

```bash
docker exec -it ch clickhouse-client -q "
SELECT 
    p.name as part_name,
    pp.name as projection_name
FROM system.parts p
LEFT JOIN system.projection_parts pp 
    ON p.database = pp.database 
    AND p.table = pp.table 
    AND p.name = pp.part_name 
    AND pp.name = 'p_hourly_all'
WHERE p.database = 'exp' 
    AND p.table = 'measurements' 
    AND p.active = 1
ORDER BY p.name;
"
```

### Verify all parts have projection

```bash
docker exec -it ch clickhouse-client -q "
SELECT
    'Main table parts:' as type,
    count() as parts
FROM system.parts
WHERE database='exp' AND table='measurements' AND active=1
UNION ALL
SELECT
    'Projection parts:' as type,
    count() as parts
FROM system.projection_parts
WHERE database='exp' AND table='measurements' AND name='p_hourly_all' AND active=1;
"
```

## Check mutations (measurements table)

```bash
docker exec -it ch clickhouse-client -q "
SELECT mutation_id, command, is_done, parts_to_do, latest_failed_part, latest_fail_reason
FROM system.mutations 
WHERE table = 'measurements';
"
```

## Fetch latest values

```bash
$ docker exec -it ch clickhouse-client --time -q "
SELECT
    id,
    ts,
    value
FROM exp.measurements
WHERE id = 1
ORDER BY ts DESC
LIMIT 10;"
1       2025-01-12 13:46:39     19.850859156048585
1       2025-01-12 13:46:39     19.809729798619426
1       2025-01-12 13:46:39     19.804472253167592
1       2025-01-12 13:46:39     19.793496090908516
1       2025-01-12 13:46:39     19.792977714601637
1       2025-01-12 13:46:39     19.78771837689943
1       2025-01-12 13:46:39     19.785856866489073
1       2025-01-12 13:46:39     19.780631764653446
1       2025-01-12 13:46:39     19.779530547838807
1       2025-01-12 13:46:39     19.777893791114316
0.006
```
