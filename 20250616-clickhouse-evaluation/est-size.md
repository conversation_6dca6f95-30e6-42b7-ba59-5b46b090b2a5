# Size estimation

- By o3: [Ingest time estimation](https://chatgpt.com/c/685030de-9eec-800b-b9b0-22c8bb6f08b2)

## Size Estimation


### Quick maths

| Item                                                     | Calculation                    | Size (GiB)                        |
|----------------------------------------------------------|--------------------------------|-----------------------------------|
| **Raw payload**                                          | 24 B/row × 63 072 000 000 rows | **≈ 1 410 GiB**                   |
| LZ4-compressed data (10 : 1 typical for numeric TS data) | ÷ 10                           | **≈ 141 GiB** ([altinity.com][1]) |
| Primary-key index (`id, ts`)   ≈12 B per granule         | 2.46 × 10⁸ granules            | \~ 3 GiB                          |
| Marks/offset files (16 B per column·granule · 4 cols)    | 16 B × 4                       | \~ 16 GiB ([clickhouse.com][2])   |
| **Expected on-disk footprint**                           | 141 + 3 + 16                   | **≈ 160 GiB**                     |

> **Rule of thumb:** with default `LZ4` the table will land in the **140 – 200 GiB** band (compression 8-12×) depending on data entropy. The figure already includes MergeTree metadata.

---

### How to squeeze it further

| Tweak                                                     | Typical extra gain | Note                                                                                                |
|-----------------------------------------------------------|--------------------|-----------------------------------------------------------------------------------------------------|
| `ts DateTime64 CODEC(DoubleDelta, LZ4)`                   | × 1.5–2            | Delta-of-delta is optimal for monotonic timestamps ([altinity.com][3])                              |
| `value Float64 CODEC(Gorilla, ZSTD(1))`                   | × 1.5              | Gorilla is built for float “gauges”; ZSTD costs more CPU but cuts bytes further ([altinity.com][4]) |
| Drop the materialised `bucket_min` and compute on-the-fly | up to 15 %         | Only helps if you don’t filter by the column frequently.                                            |
| Raise `index_granularity` to 8192                         | \~-5 GiB           | Fewer marks, slightly slower point look-ups.                                                        |

With those codecs and a small schema tidy-up you can get **≈ 90 – 110 GiB** total.

If you want a quick reality check on a live sample, run:

```sql
SELECT
    estimateCompressionRatio(ts)        AS ts_ratio,
    estimateCompressionRatio(value)     AS value_ratio
FROM exp.measurements LIMIT 100000;
```

The aggregate will tell you how well each column compresses with the server’s default codec ([clickhouse.com][5]).

---

### Defaults & references

* Self-managed ClickHouse uses **`LZ4` by default** when no `CODEC()` is specified ([clickhouse.com][6]).
* MergeTree keeps a **small uncompressed mark file per column per granule**; size grows linearly with row-count and inversely with `index_granularity` ([clickhouse.com][2]).

That’s all—you can budget roughly **160 GiB** today, and halve it later by adding the right codecs.

[1]: https://altinity.com/blog/clickhouse-for-time-series "ClickHouse® Crushing Time Series - Altinity | Run open source ClickHouse® better"
[2]: https://clickhouse.com/docs/engines/table-engines/mergetree-family/mergetree?utm_source=chatgpt.com "MergeTree | ClickHouse Docs"
[3]: https://altinity.com/blog/2019-7-new-encodings-to-improve-clickhouse?utm_source=chatgpt.com "New Encodings to Improve ClickHouse® Efficiency - Altinity"
[4]: https://altinity.com/blog/2020-1-1-clickhouse-cost-efficiency-in-action-analyzing-500-billion-rows-on-an-intel-nuc?utm_source=chatgpt.com "ClickHouse® Cost-Efficiency in Action: Analyzing 500 Billion Rows ..."
[5]: https://clickhouse.com/docs/sql-reference/aggregate-functions/reference/estimateCompressionRatio "estimateCompressionRatio | ClickHouse Docs"
[6]: https://clickhouse.com/docs/en/sql-reference/statements/create/table "CREATE TABLE | ClickHouse Docs"

---
