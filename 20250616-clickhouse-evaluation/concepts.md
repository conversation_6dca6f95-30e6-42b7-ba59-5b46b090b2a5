# ClickHouse concepts

## Recommended Maximum Databases, Tables, Partitions, and Parts in ClickHouse

https://clickhouse.com/docs/knowledgebase/maximum_number_of_tables_and_databases

We recommend having a maximum of 1000 databases and 5000 tables, 50000 partitions, and 100000 parts across all databases for a service.

Databases in ClickHouse are more of a namespace and have no performance impact; 1000 databases is a loose guideline. However, the number of tables would affect the service startup time, so we recommend limiting the number of tables or partitions. ClickHouse provides a warning if these thresholds are hit.

These limits are also applicable to ClickHouse Cloud.

## ClickHouse Partition Size Guidelines (LLM)

Looking at the `PARTITION BY toYYYYMM(ts)` requirement in your ClickHouse table definition, I can explain the partitioning considerations:

When designing partitioning for ClickHouse tables, there are several size considerations:

1. **Optimal partition size**:
   - Target 10-100GB of uncompressed data per partition (typically 1-10GB after compression)
   - Roughly 10-30 million rows per partition is generally reasonable

2. **Balance considerations**:
   - **Too large partitions**: Slow operations like DROP/DETACH PARTITION, less efficient partition pruning
   - **Too small partitions**: File system overhead, excessive merge processes, higher metadata overhead

3. **Why monthly partitioning (`toYYYYMM`) works well**:
   - For most time-series data, monthly partitioning provides a good balance
   - Efficient for range queries spanning weeks to months
   - Simplifies data lifecycle management (retention policies, archiving)

4. **When to adjust partitioning granularity**:
   - **Higher volume**: If ingesting billions of rows daily, consider `toYYYYMMDD` (daily partitions)
   - **Lower volume**: If data is sparse, consider quarterly or yearly partitioning

The current `PARTITION BY toYYYYMM(ts)` is appropriate for the 1B row example dataset, where each month would contain roughly 83M rows (assuming even distribution), which falls within recommended guidelines.
