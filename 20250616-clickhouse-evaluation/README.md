# ClickHouse evaluation

- **Author(s)**:
  - <PERSON>. unTill Software Development Group B. V.
  - <PERSON>. unTill Software Development Group B. V.
- **Start date**: 2026-06-16
- **Keywords**: clickhouse, OLAP, SCADA

## Abstract

This evaluation assesses ClickHouse as a columnar OLAP database solution for storing and analyzing large volumes of time-series measurement data. The study addresses current limitations in Air (complex cross-workspace reporting) and Nestor (inefficient storage of OLTP data in row-oriented dynobuffer format) by evaluating ClickHouse's performance with 2 billion synthetic temperature measurements.

The evaluation demonstrates ClickHouse's capability to handle massive time-series datasets efficiently. Using a single-node Docker deployment on commodity hardware (Intel i7 with 32GB RAM), the system successfully ingested 2 billion measurements in approximately 8 minutes (≈4.2M rows/second) and achieved significant data compression (11.89 GiB for 2B rows, ≈5.6 bytes per row) through specialized codecs (Delta, Gorilla, ZSTD). Query performance proved excellent, with single-sensor hourly aggregations over 14-day periods completing in 0.006 seconds, demonstrating ClickHouse's suitability for real-time analytical workloads on large-scale measurement data.

## Motivation

- Air: Reports per workspace are possible, cross-workspace reports are very complicated
- Nestor: Data in OLTP format (row-oriented dynobuffer) takes a lot of space

Proposed solution

- Use OLAP columnar database ClickHouse for storing and retrieving data
  - High performance for analytical processing large volumes of data solves of cross-workspace reports
  - High compression ratio (columnar database) solves the problem of large data storage

## Goals

- Evaluate the complexity of ClickHouse installation and operation
- Evaluate performance for writing and reading operations

## Method

Overview:

- Develop a scheme - one table with raw data, minute-by-minute aggregations
- Install ClickHouse on a local machine using Docker
- Insert raw data records
- Examine how queries work

Detailed plan:

- [ClickHouse per minute aggregation](clickhouse-min-aggr.md)

## Background

- [ClickHouse Playground](https://sql.clickhouse.com/)
- [ClickHouse concepts](concepts.md)
- [Nestor schemas](nestor-schemas.md)

---

## Results

### 2B Measurements Evaluation

- Executed the test method with 2 billion measurements
- The first attempt: [worklog-2B.md](worklog-2B.md)
- Data compression experiments: [worklog-2B-compression.md](worklog-2B-compression.md)

## Key findings

### Performance Results

**Ingestion Performance:**

- Successfully ingested 2 billion measurements in ~8 minutes (≈4.2M rows/second)
- Consistent performance across different hardware configurations (Intel i7-7700 and i7-12700)
- Single INSERT operation automatically batched into 65,536-row chunks for optimal throughput

**Storage Efficiency:**

- Achieved 11.89 GiB compressed storage for 2 billion rows (≈5.6 bytes per row)
- Compression ratio improved significantly with optimized codecs:
  - `UInt32` sensor IDs: Delta + ZSTD compression
  - `DateTime64` timestamps: Delta + ZSTD compression
  - `Float64` values: Gorilla + ZSTD compression
- Initial attempts with random data showed poor compression (50+ GiB), highlighting the importance of realistic data patterns

**Query Performance:**

- Single sensor hourly aggregations (14-day period): 0.006 seconds
- All sensors hourly aggregations (14-day period): 8.6 seconds
- Latest value queries across 100 sensors: 5.3 seconds
- Per-minute projections enable sub-second analytical queries

### Technical Insights

**Data Modeling:**

- Realistic synthetic data generation crucial for accurate compression evaluation
- Temperature model included: deterministic sensor offsets, diurnal cycles, seasonal drift, controlled noise, and rare spikes
- Materialized columns (e.g., `bucket_min`) enable efficient time-based aggregations

**Deployment Simplicity:**

- Single-node Docker deployment sufficient for 2B+ row datasets
- Minimal configuration required - ClickHouse defaults work well for time-series data
- No external dependencies (Zookeeper, etc.) needed for single-node evaluation

**Limitations Identified:**

- Projections don't always optimize as expected - manual query optimization may be required
- Background materialization of projections can consume significant CPU resources
- All-sensor aggregations slower than single-sensor queries, suggesting need for specialized projections

### Recommendations

1. **For Production Deployment:** ClickHouse demonstrates excellent suitability for large-scale time-series analytics with proper codec selection and schema design
2. **Hardware Requirements:** Commodity hardware (4+ cores, 16+ GB RAM, NVMe storage) sufficient for multi-billion row datasets
3. **Data Architecture:** Columnar storage with specialized compression codecs provides 10x+ storage efficiency compared to row-oriented approaches
4. **Query Optimization:** Pre-aggregated projections essential for sub-second response times on large datasets
