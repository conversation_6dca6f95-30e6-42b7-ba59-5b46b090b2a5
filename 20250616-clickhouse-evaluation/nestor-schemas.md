# Nestor schemas

## Disk size estimation

2 160 000 000  команд  займут ~ 260Gb

## MeasureAggregateView

```mermaid
erDiagram

MeasureAggregateView ||--|| Key : key
Key ||--|| PartitionKey : PK
Key ||--|| ClusteringColumns : CC
MeasureAggregateView ||--|| Value : value

PartitionKey {
    int32 id "ID измерения"
    int32 period "тип периода"
    int32 cluster "кластер"
}

ClusteringColumns {
    int16 year "год"
    int8 month "месяц"
    int8 day "день"
    int8 hour "час"
    int8 minute "минута"
    int8 second "секунда"
}

Value {
    int32 cnt "количество измерений"
    float64 sum  "сумма значений"
    float64 min "минимальное значение"
    float64 max "максимальное значение"
    int64 wlog "последнее событие"
}
```

## PLog/WLog

```sql
TYPE MeasureReportResult (
    id int32 NOT NULL,
    desc varchar,
    year int32 NOT NULL,
    month int32 NOT NULL,
    day int32 NOT NULL,
    hour int32 NOT NULL,
    minute int32 NOT NULL,
    second int32 NOT NULL,
    value float64 NOT NULL
);
```

## MeasureDoublesView

Только если событие дубль. Если событие уникально, то запись о нем в дублях отсуствует

```mermaid
erDiagram
MeasureDoublesView ||--|| Key : key
Key ||--|| PartitionKey : PK
Key ||--|| ClusteringColumns : CC
MeasureDoublesView ||--|| Value : value

PartitionKey {
    int64 wlog_hi "старшие 40 бит смещения события-дубля в журнале"
}

ClusteringColumns {
    int32 wlog_lo "младшие 24 бита смещения события-дубля в журнале"
}

Value {
    int64 wlog "смещение события-оригинала в журнале"
}
```