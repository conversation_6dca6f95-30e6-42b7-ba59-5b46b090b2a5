package main

import (
	"fmt"
	"log"
	"math/rand"
	"runtime"
	"strconv"
	"sync"
	"testing"
	"time"

	"github.com/VictoriaMetrics/fastcache"
	"github.com/montanaflynn/stats"
)

type Setter interface {
	Set(k, v string)
}

var hugeMap map[string][]byte
var hugeMapMutex sync.Mutex

const N = 20e6

var testDataKeys [][]byte
var testDataValues [][]byte

const TestN = 10000

func requireNil(b *testing.B, val interface{}) {
	if val != nil {
		log.Fatal(val)
	}
}

func initTestData() {
	if nil != testDataKeys {
		return
	}

	testDataKeys = make([][]byte, TestN)
	testDataValues = make([][]byte, TestN)
	for i := 0; i < TestN; i++ {
		idx := rand.Int() % N
		v := strconv.Itoa(idx)
		testDataKeys[i] = []byte(v)
		testDataValues[i] = []byte(v)
	}

	return
}
func initHugeMap() {
	if nil == hugeMap {
		hugeMap = make(map[string][]byte)
		for i := 0; i < N; i++ {
			n := strconv.Itoa(i)
			hugeMap[n] = []byte(n)
		}
	}
}

var touchStatMutex sync.Mutex
var touchDurations []float64

func touchHugeMap() {
	start := time.Now()
	for idx, kSrc := range testDataKeys {
		vSrc := testDataValues[idx]

		k := string(kSrc)
		v := make([]byte, 0, len(vSrc))
		v = append(v, vSrc...)

		hugeMapMutex.Lock()

		hugeMap[k] = v
		hugeMapMutex.Unlock()
	}
	elapsed := (time.Since(start).Nanoseconds())
	touchStatMutex.Lock()
	touchDurations = append(touchDurations, float64(elapsed))
	touchStatMutex.Unlock()

}

func Test_touchHugeMap(t *testing.T) {
	initHugeMap()
	start := time.Now().UnixNano()
	touchHugeMap()
	end := time.Now().UnixNano()
	fmt.Println(end - start)
	fmt.Println(touchDurations)

}

func Test_huge_cycles_map(t *testing.T) {
	initHugeMap()
	initTestData()
	for i := 0; i < 1000; i++ {
		start := time.Now()
		touchHugeMap()
		elapsed := time.Since(start).Seconds()
		fmt.Println("Touch cycle", i, elapsed)
	}
}

func gcrunner(done chan string) {
	defer func() {
		log.Println("gcrunner finished")
		done <- "gcrunner"
	}()
	gcduration := 100 * time.Millisecond
	timer := time.NewTimer(gcduration)

	for {
		select {
		case <-done:
			return
		case <-timer.C:
			startGC := time.Now()
			runtime.GC()
			gcduration = time.Since(startGC)
			log.Println("gcduration", gcduration)
			if gcduration < 100 * time.Millisecond{
				gcduration = 100 * time.Millisecond
			}
			timer = time.NewTimer(gcduration)
		}
	}

}

func Test_add_map(t *testing.T) {
	done := make(chan string)
	go gcrunner(done)

	huge := make([]*int, 8e8)
	log.Println("len(huge)", len(huge))

	time.Sleep(time.Second * 5)
	done <- "exit"
	<-done

}

func Benchmark_huge_map(b *testing.B) {

	var ms runtime.MemStats
	runtime.ReadMemStats(&ms)

	done := make(chan string)
	go gcrunner(done)

	initHugeMap()
	initTestData()
	b.ResetTimer()
	start := time.Now()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			touchHugeMap()
		}
	})

	b.StopTimer()
	done <- "exit"
	<-done

	elapsed := time.Since(start).Seconds()
	rps := float64(b.N) / elapsed

	max, e := stats.Max(touchDurations)
	requireNil(b, e)

	p95, e := stats.Percentile(touchDurations, 95)
	requireNil(b, e)

	p98, e := stats.Percentile(touchDurations, 98)
	requireNil(b, e)

	p995, e := stats.Percentile(touchDurations, 99.5)
	requireNil(b, e)

	mean, e := stats.Mean(touchDurations)
	requireNil(b, e)

	fmt.Println("\n========")
	var ms2 runtime.MemStats
	runtime.ReadMemStats(&ms2)
	fmt.Println("b.N", b.N)
	fmt.Println("ms.NumGC", ms2.NumGC-ms.NumGC)
	fmt.Println("mean", mean)
	fmt.Println("p95", p95)
	fmt.Println("p98", p98)
	fmt.Println("p99.5", p995)
	fmt.Println("max", max)
	fmt.Println("rps", rps)
}

var hugeFastcahe *fastcache.Cache

func Benchmark_huge_fastcache(b *testing.B) {
	if nil == hugeFastcahe {
		// 50 bytes per key and 50 per value
		hugeFastcahe = fastcache.New(N * (50 + 50))
		for i := 0; i < N; i++ {
			n := strconv.Itoa(i)
			hugeFastcahe.Set([]byte(n), []byte(n))
		}
	}

	initTestData()
	b.ResetTimer()
	//	start := time.Now()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			for idx, kSrc := range testDataKeys {
				vSrc := testDataValues[idx]
				hugeFastcahe.Set(kSrc, vSrc)
			}
		}
	})
	//	elapsed := time.Since(start).Seconds()
	//	rps := float64(b.N) / elapsed

}
